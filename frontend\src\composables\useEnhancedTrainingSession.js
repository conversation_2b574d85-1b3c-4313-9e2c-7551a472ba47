/**
 * 增强版训练会话管理
 * 整合多维度动作检测、智能反馈和声音提示
 */
import { ref, computed, watch } from 'vue'
import { useEnhancedActionDetection } from './useEnhancedActionDetection'
import { useSmartFeedback } from './useSmartFeedback'
import { useAudioFeedback } from './useAudioFeedback'
import { useStateTransition } from './useStateTransition'
import { useTrainingStore } from '@/stores/training'
import { useConnectionStore } from '@/stores/connection'
import { useNotificationStore } from '@/stores/notification'

export function useEnhancedTrainingSession() {
  const actionDetection = useEnhancedActionDetection()
  const smartFeedback = useSmartFeedback()
  const audioFeedback = useAudioFeedback()
  const stateTransition = useStateTransition()
  const trainingStore = useTrainingStore()
  const connectionStore = useConnectionStore()
  const notificationStore = useNotificationStore()

  // 训练会话状态
  const isTrainingActive = ref(false)
  const actionStartTime = ref(null)
  const actionHoldTime = ref(0)
  const actionHoldTimer = ref(null)
  const scoreHistory = ref([])
  const lastScores = ref({ overall: 0, accuracy: 0, stability: 0, completeness: 0 })
  const thresholdReached = ref(false)

  // 检测配置
  const DETECTION_CONFIG = {
    sampleInterval: 200, // 检测间隔（毫秒）
    scoreThreshold: 85, // 动作完成阈值
    holdDuration: 2000, // 保持时间（毫秒）
    feedbackInterval: 3000 // 反馈间隔（毫秒）
  }

  // 计算属性
  const currentActionType = computed(() => trainingStore.currentAction?.action_type)
  const currentActionSide = computed(() => trainingStore.currentAction?.side || 'left')
  const currentDifficultyLevel = computed(() => trainingStore.currentAction?.difficulty_level || 'easy')
  
  const isActionInProgress = computed(() => isTrainingActive.value && !!currentActionType.value)
  
  const actionCompletionProgress = computed(() => {
    if (!isActionInProgress.value) return 0
    return Math.min(100, (actionHoldTime.value / DETECTION_CONFIG.holdDuration) * 100)
  })

  const currentScores = computed(() => actionDetection.currentScores.value)
  const visibilityStatus = computed(() => actionDetection.visibilityStatus.value)

  /**
   * 开始训练会话
   */
  const startTrainingSession = () => {
    if (isTrainingActive.value) return

    console.log('[EnhancedTrainingSession] 开始增强训练会话')
    console.log('[EnhancedTrainingSession] 当前动作类型:', currentActionType.value)
    console.log('[EnhancedTrainingSession] 当前动作侧面:', currentActionSide.value)
    console.log('[EnhancedTrainingSession] 当前难度等级:', currentDifficultyLevel.value)
    console.log('[EnhancedTrainingSession] 当前分数状态:', actionDetection.currentScores.value)

    isTrainingActive.value = true
    actionDetection.isDetecting.value = true

    // 重置状态
    resetActionState()

    // 开始实时检测
    startRealtimeDetection()
  }

  /**
   * 停止训练会话
   */
  const stopTrainingSession = () => {
    if (!isTrainingActive.value) return

    console.log('[EnhancedTrainingSession] 停止增强训练会话')
    isTrainingActive.value = false
    actionDetection.isDetecting.value = false
    
    // 停止所有音频
    audioFeedback.stopAllAudio()
    
    // 重置状态
    resetActionState()
  }

  /**
   * 开始实时动作检测
   */
  const startRealtimeDetection = () => {
    console.log('[EnhancedTrainingSession] 启动实时检测')
    console.log('[EnhancedTrainingSession] 检测配置:', DETECTION_CONFIG)

    const detectionInterval = setInterval(() => {
      if (!isTrainingActive.value || !currentActionType.value) {
        console.log('[EnhancedTrainingSession] 检测停止条件触发:', {
          isTrainingActive: isTrainingActive.value,
          currentActionType: currentActionType.value
        })
        clearInterval(detectionInterval)
        return
      }

      // 获取当前姿态关键点
      const keypoints = connectionStore.poseKeypoints
      if (!keypoints || keypoints.length === 0) {
        console.log('[EnhancedTrainingSession] 没有姿态关键点数据，连接状态:', connectionStore.isConnected)
        return
      }

      console.log('[EnhancedTrainingSession] 检测到关键点数据，数量:', keypoints.length)

      // 执行增强检测
      const detectionResult = actionDetection.detectActionEnhanced(
        currentActionType.value,
        keypoints,
        currentActionSide.value,
        currentDifficultyLevel.value,
        640, // 画布宽度，可以从实际组件获取
        480  // 画布高度，可以从实际组件获取
      )

      // 处理检测结果
      handleDetectionResult(detectionResult)

    }, DETECTION_CONFIG.sampleInterval)
  }

  /**
   * 处理检测结果
   */
  const handleDetectionResult = (detectionResult) => {
    const { overall, accuracy, stability, completeness, feedback } = detectionResult

    console.log('[EnhancedTrainingSession] 检测结果:', {
      overall, accuracy, stability, completeness, feedback
    })

    // 记录分数历史
    scoreHistory.value.push({
      timestamp: Date.now(),
      overall,
      accuracy,
      stability,
      completeness,
      actionType: currentActionType.value
    })

    // 保持历史记录在合理范围内
    if (scoreHistory.value.length > 100) {
      scoreHistory.value = scoreHistory.value.slice(-50)
    }

    // 处理画面不完整情况
    if (completeness < 100) {
      handleFrameIncomplete()
      return
    }

    // 处理智能反馈
    handleSmartFeedback(detectionResult)

    // 检查动作完成条件
    checkActionCompletion(overall)

    // 更新最后分数记录
    lastScores.value = { overall, accuracy, stability, completeness }
  }

  /**
   * 处理画面不完整
   */
  const handleFrameIncomplete = () => {
    // 重置动作计时
    resetActionHoldTimer()
    
    // 播放警告音（限制频率）
    const now = Date.now()
    const lastWarning = scoreHistory.value
      .filter(s => s.completeness < 100)
      .slice(-1)[0]
    
    if (!lastWarning || now - lastWarning.timestamp > 5000) {
      audioFeedback.playFrameWarningAudio()
    }
  }

  /**
   * 处理智能反馈
   */
  const handleSmartFeedback = (detectionResult) => {
    const currentScores = {
      overall: detectionResult.overall,
      accuracy: detectionResult.accuracy,
      stability: detectionResult.stability,
      completeness: detectionResult.completeness
    }

    // 生成并显示智能反馈
    const feedbackShown = smartFeedback.handleScoreChange(
      currentActionType.value,
      currentActionSide.value,
      currentScores,
      lastScores.value,
      {
        visibilityMessage: visibilityStatus.value.message,
        ...detectionResult
      }
    )

    // 如果显示了反馈，播放相应音效
    if (feedbackShown && smartFeedback.currentGuidance.value) {
      const guidance = smartFeedback.currentGuidance.value
      
      // 根据反馈类型播放不同音效
      if (guidance.includes('画面')) {
        audioFeedback.playFrameWarningAudio()
      } else if (guidance.includes('保持') || guidance.includes('很好')) {
        // 鼓励性反馈，播放轻柔提示音
        audioFeedback.playAudio('guidance', { volume: 0.3 })
      } else {
        // 指导性反馈，播放指导音效
        audioFeedback.playGuidanceAudio(
          currentActionType.value,
          currentActionSide.value,
          'general'
        )
      }
    }
  }

  /**
   * 检查动作完成条件
   */
  const checkActionCompletion = (currentScore) => {
    if (currentScore >= DETECTION_CONFIG.scoreThreshold) {
      // 开始或继续计时
      if (!actionStartTime.value) {
        actionStartTime.value = Date.now()
        thresholdReached.value = true
        
        // 播放达标提示音
        audioFeedback.playScoreThresholdAudio(currentScore)
        
        startActionHoldTimer()
      }
    } else {
      // 分数不够，重置计时
      if (thresholdReached.value) {
        thresholdReached.value = false
        resetActionHoldTimer()
      }
    }
  }

  /**
   * 开始动作保持计时
   */
  const startActionHoldTimer = () => {
    if (actionHoldTimer.value) return

    actionHoldTimer.value = setInterval(() => {
      if (!actionStartTime.value) {
        clearInterval(actionHoldTimer.value)
        actionHoldTimer.value = null
        return
      }

      actionHoldTime.value = Date.now() - actionStartTime.value

      // 检查是否达到保持时间要求
      if (actionHoldTime.value >= DETECTION_CONFIG.holdDuration) {
        handleActionCompleted()
      }
    }, 50) // 50ms更新一次进度
  }

  /**
   * 重置动作保持计时
   */
  const resetActionHoldTimer = () => {
    if (actionHoldTimer.value) {
      clearInterval(actionHoldTimer.value)
      actionHoldTimer.value = null
    }
    actionStartTime.value = null
    actionHoldTime.value = 0
    thresholdReached.value = false
  }

  /**
   * 处理动作完成
   */
  const handleActionCompleted = () => {
    if (!currentActionType.value) return

    console.log(`[EnhancedTrainingSession] 动作完成: ${currentActionType.value}`)
    
    // 计算最终得分
    const recentScores = scoreHistory.value
      .filter(record => record.actionType === currentActionType.value)
      .slice(-10)
      .map(record => record.overall)

    const finalScore = recentScores.length > 0 
      ? Math.round(recentScores.reduce((sum, score) => sum + score, 0) / recentScores.length)
      : currentScores.value.overall

    // 播放完成庆祝音效
    const celebration = smartFeedback.getCompletionCelebration(finalScore)
    audioFeedback.playCelebrationAudio(celebration.level, finalScore)

    // 重置动作状态
    resetActionState()

    // 触发状态转换
    stateTransition.handleActionComplete(finalScore)
  }

  /**
   * 重置动作状态
   */
  const resetActionState = () => {
    resetActionHoldTimer()
    scoreHistory.value = []
    lastScores.value = { overall: 0, accuracy: 0, stability: 0, completeness: 0 }
    thresholdReached.value = false
    smartFeedback.resetFeedback()
  }

  return {
    // 响应式数据
    isTrainingActive,
    actionHoldTime,
    scoreHistory,
    thresholdReached,

    // 计算属性
    currentActionType,
    currentActionSide,
    currentDifficultyLevel,
    isActionInProgress,
    actionCompletionProgress,
    currentScores,
    visibilityStatus,

    // 智能反馈
    currentGuidance: smartFeedback.currentGuidance,
    shouldShowGuidance: smartFeedback.shouldShowGuidance,

    // 核心方法
    startTrainingSession,
    stopTrainingSession,
    handleActionCompleted,

    // 内部方法（暴露用于调试）
    resetActionState
  }
}
