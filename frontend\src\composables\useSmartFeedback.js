/**
 * 智能反馈系统
 * 根据用户动作执行情况生成智能指导，控制反馈频率
 */
import { ref, computed } from 'vue'

export function useSmartFeedback() {
  // 反馈状态
  const lastFeedbackTime = ref(0)
  const lastFeedbackType = ref('')
  const feedbackHistory = ref([])
  const currentGuidance = ref('')
  const shouldShowGuidance = ref(false)

  // 反馈配置
  const FEEDBACK_CONFIG = {
    minInterval: 3000, // 最小反馈间隔（毫秒）
    scoreChangeThreshold: 10, // 分数变化阈值
    repetitionLimit: 2, // 相同类型反馈的重复限制
    guidanceDisplayTime: 4000 // 指导信息显示时间
  }

  // 反馈类型定义
  const FEEDBACK_TYPES = {
    POSITION: 'position',
    ANGLE: 'angle',
    DISTANCE: 'distance',
    STABILITY: 'stability',
    COMPLETENESS: 'completeness',
    ENCOURAGEMENT: 'encouragement'
  }

  /**
   * 生成动作指导
   */
  const generateActionGuidance = (actionType, side, scores, detectionData) => {
    const { accuracy, stability, completeness } = scores
    const sideText = side === 'left' ? '左' : '右'
    
    // 优先级：完整性 > 准确性 > 稳定性
    if (completeness < 100) {
      return {
        type: FEEDBACK_TYPES.COMPLETENESS,
        message: detectionData.visibilityMessage || `请保持${sideText}侧关键部位在画面内`,
        priority: 1
      }
    }

    if (accuracy < 60) {
      return generateAccuracyGuidance(actionType, side, accuracy, detectionData)
    }

    if (stability < 70) {
      return {
        type: FEEDBACK_TYPES.STABILITY,
        message: '请保持动作稳定，避免抖动',
        priority: 3
      }
    }

    if (accuracy >= 80) {
      return {
        type: FEEDBACK_TYPES.ENCOURAGEMENT,
        message: getEncouragementMessage(accuracy),
        priority: 4
      }
    }

    return {
      type: FEEDBACK_TYPES.ENCOURAGEMENT,
      message: '继续保持，动作不错',
      priority: 4
    }
  }

  /**
   * 生成准确性指导
   */
  const generateAccuracyGuidance = (actionType, side, accuracy, detectionData) => {
    const sideText = side === 'left' ? '左' : '右'
    
    switch (actionType) {
      case 'shoulder_touch':
        if (detectionData.targetDistance > detectionData.referenceDistance * 1.5) {
          return {
            type: FEEDBACK_TYPES.DISTANCE,
            message: `请将${sideText}手更靠近${side === 'left' ? '右' : '左'}肩`,
            priority: 2
          }
        }
        break

      case 'arm_raise':
        if (detectionData.heightScore < 60) {
          return {
            type: FEEDBACK_TYPES.POSITION,
            message: `请将${sideText}手举得更高`,
            priority: 2
          }
        }
        if (detectionData.angleScore < 60) {
          return {
            type: FEEDBACK_TYPES.ANGLE,
            message: `请将${sideText}臂伸得更直`,
            priority: 2
          }
        }
        break

      case 'finger_touch':
        if (detectionData.fingerDistance > detectionData.expectedDistance * 2) {
          return {
            type: FEEDBACK_TYPES.DISTANCE,
            message: '请将拇指和食指靠得更近',
            priority: 2
          }
        }
        break

      case 'palm_flip':
        if (detectionData.angleRange < 45) {
          return {
            type: FEEDBACK_TYPES.ANGLE,
            message: `请加大${sideText}手翻转幅度`,
            priority: 2
          }
        }
        break
    }

    return {
      type: FEEDBACK_TYPES.POSITION,
      message: '请参考标准动作调整姿态',
      priority: 2
    }
  }

  /**
   * 获取鼓励信息
   */
  const getEncouragementMessage = (accuracy) => {
    if (accuracy >= 95) {
      return '完美！动作非常标准'
    } else if (accuracy >= 85) {
      return '很好！继续保持'
    } else {
      return '不错，再加把劲'
    }
  }

  /**
   * 检查是否应该显示反馈
   */
  const shouldShowFeedback = (feedbackType, currentScore, lastScore) => {
    const now = Date.now()
    
    // 检查时间间隔
    if (now - lastFeedbackTime.value < FEEDBACK_CONFIG.minInterval) {
      return false
    }

    // 检查分数变化
    const scoreChange = Math.abs(currentScore - lastScore)
    if (scoreChange < FEEDBACK_CONFIG.scoreChangeThreshold && feedbackType !== FEEDBACK_TYPES.COMPLETENESS) {
      return false
    }

    // 检查重复性
    const recentSameTypeFeedback = feedbackHistory.value
      .filter(f => f.type === feedbackType && now - f.timestamp < 10000)
      .length

    if (recentSameTypeFeedback >= FEEDBACK_CONFIG.repetitionLimit) {
      return false
    }

    return true
  }

  /**
   * 显示指导信息
   */
  const showGuidance = (guidance) => {
    if (!shouldShowFeedback(guidance.type, 0, 0)) {
      return false
    }

    currentGuidance.value = guidance.message
    shouldShowGuidance.value = true
    lastFeedbackTime.value = Date.now()
    lastFeedbackType.value = guidance.type

    // 记录反馈历史
    feedbackHistory.value.push({
      timestamp: Date.now(),
      type: guidance.type,
      message: guidance.message,
      priority: guidance.priority
    })

    // 保持历史记录在合理范围内
    if (feedbackHistory.value.length > 50) {
      feedbackHistory.value = feedbackHistory.value.slice(-25)
    }

    // 自动隐藏指导信息
    setTimeout(() => {
      shouldShowGuidance.value = false
    }, FEEDBACK_CONFIG.guidanceDisplayTime)

    return true
  }

  /**
   * 处理分数变化反馈
   */
  const handleScoreChange = (actionType, side, newScores, oldScores, detectionData) => {
    // 生成指导建议
    const guidance = generateActionGuidance(actionType, side, newScores, detectionData)
    
    // 检查是否应该显示
    if (shouldShowFeedback(guidance.type, newScores.overall, oldScores?.overall || 0)) {
      return showGuidance(guidance)
    }

    return false
  }

  /**
   * 获取完成庆祝信息
   */
  const getCompletionCelebration = (finalScore) => {
    if (finalScore >= 95) {
      return {
        message: '太棒了！完美完成！',
        level: 'excellent',
        audioType: 'celebration_excellent'
      }
    } else if (finalScore >= 85) {
      return {
        message: '很好！动作完成得很棒！',
        level: 'good',
        audioType: 'celebration_good'
      }
    } else if (finalScore >= 75) {
      return {
        message: '不错！继续努力！',
        level: 'fair',
        audioType: 'celebration_fair'
      }
    } else {
      return {
        message: '还要加把劲，继续练习！',
        level: 'poor',
        audioType: 'encouragement'
      }
    }
  }

  /**
   * 重置反馈状态
   */
  const resetFeedback = () => {
    lastFeedbackTime.value = 0
    lastFeedbackType.value = ''
    feedbackHistory.value = []
    currentGuidance.value = ''
    shouldShowGuidance.value = false
  }

  /**
   * 获取反馈统计
   */
  const getFeedbackStats = () => {
    const now = Date.now()
    const recentFeedback = feedbackHistory.value.filter(f => now - f.timestamp < 60000) // 最近1分钟
    
    return {
      totalFeedback: feedbackHistory.value.length,
      recentFeedback: recentFeedback.length,
      lastFeedbackTime: lastFeedbackTime.value,
      currentGuidance: currentGuidance.value,
      isShowingGuidance: shouldShowGuidance.value
    }
  }

  return {
    // 响应式数据
    currentGuidance,
    shouldShowGuidance,
    feedbackHistory,

    // 核心方法
    generateActionGuidance,
    handleScoreChange,
    showGuidance,
    getCompletionCelebration,

    // 工具方法
    resetFeedback,
    getFeedbackStats,

    // 常量
    FEEDBACK_TYPES
  }
}
