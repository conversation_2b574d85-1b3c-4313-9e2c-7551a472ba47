/**
 * 训练会话管理
 * 整合动作检测和状态转换，管理完整的训练流程
 */
import { ref, computed, watch } from 'vue'
import { useActionDetection } from './useActionDetection'
import { useStateTransition } from './useStateTransition'
import { useTrainingStore } from '@/stores/training'
import { useConnectionStore } from '@/stores/connection'
import { useNotificationStore } from '@/stores/notification'

export function useTrainingSession() {
  const actionDetection = useActionDetection()
  const stateTransition = useStateTransition()
  const trainingStore = useTrainingStore()
  const connectionStore = useConnectionStore()
  const notificationStore = useNotificationStore()

  // 训练会话状态
  const isTrainingActive = ref(false)
  const actionStartTime = ref(null)
  const actionHoldTime = ref(0)
  const actionHoldTimer = ref(null)
  const scoreHistory = ref([])
  const realtimeScore = ref(0)

  // 动作完成检测配置
  const SCORE_THRESHOLD = 85 // 动作完成的最低得分
  const HOLD_DURATION = 2000 // 需要保持动作的时间（毫秒）
  const SCORE_SAMPLE_INTERVAL = 100 // 得分采样间隔（毫秒）

  // 计算属性
  const currentActionType = computed(() => {
    return trainingStore.currentAction?.action_type
  })

  const isActionInProgress = computed(() => {
    return isTrainingActive.value && !!currentActionType.value
  })

  const actionCompletionProgress = computed(() => {
    if (!isActionInProgress.value) return 0
    return Math.min(100, (actionHoldTime.value / HOLD_DURATION) * 100)
  })

  /**
   * 开始训练会话
   */
  const startTrainingSession = () => {
    if (isTrainingActive.value) return

    console.log('[TrainingSession] 开始训练会话')
    isTrainingActive.value = true
    actionDetection.startDetection()
    
    // 开始实时动作检测
    startRealtimeDetection()
  }

  /**
   * 停止训练会话
   */
  const stopTrainingSession = () => {
    if (!isTrainingActive.value) return

    console.log('[TrainingSession] 停止训练会话')
    isTrainingActive.value = false
    actionDetection.stopDetection()
    
    // 停止实时检测
    stopRealtimeDetection()
    resetActionState()
  }

  /**
   * 开始实时动作检测
   */
  const startRealtimeDetection = () => {
    const detectionInterval = setInterval(() => {
      if (!isTrainingActive.value || !currentActionType.value) {
        clearInterval(detectionInterval)
        return
      }

      // 获取当前姿态关键点
      const keypoints = connectionStore.poseKeypoints
      if (!keypoints || keypoints.length === 0) return

      // 检测当前动作
      const score = actionDetection.detectAction(currentActionType.value, keypoints)
      realtimeScore.value = score

      // 记录得分历史
      scoreHistory.value.push({
        timestamp: Date.now(),
        score,
        actionType: currentActionType.value
      })

      // 保持历史记录在合理范围内
      if (scoreHistory.value.length > 100) {
        scoreHistory.value = scoreHistory.value.slice(-50)
      }

      // 检查动作完成条件
      checkActionCompletion(score)

    }, SCORE_SAMPLE_INTERVAL)
  }

  /**
   * 停止实时检测
   */
  const stopRealtimeDetection = () => {
    // 实时检测通过interval管理，会在startRealtimeDetection中自动清理
  }

  /**
   * 检查动作完成条件
   */
  const checkActionCompletion = (currentScore) => {
    if (currentScore >= SCORE_THRESHOLD) {
      // 开始计时或继续计时
      if (!actionStartTime.value) {
        actionStartTime.value = Date.now()
        startActionHoldTimer()
      }
    } else {
      // 得分不够，重置计时
      resetActionHoldTimer()
    }
  }

  /**
   * 开始动作保持计时
   */
  const startActionHoldTimer = () => {
    if (actionHoldTimer.value) return

    actionHoldTimer.value = setInterval(() => {
      if (!actionStartTime.value) {
        clearInterval(actionHoldTimer.value)
        actionHoldTimer.value = null
        return
      }

      actionHoldTime.value = Date.now() - actionStartTime.value

      // 检查是否达到保持时间要求
      if (actionHoldTime.value >= HOLD_DURATION) {
        handleActionCompleted()
      }
    }, 50) // 50ms更新一次进度
  }

  /**
   * 重置动作保持计时
   */
  const resetActionHoldTimer = () => {
    if (actionHoldTimer.value) {
      clearInterval(actionHoldTimer.value)
      actionHoldTimer.value = null
    }
    actionStartTime.value = null
    actionHoldTime.value = 0
  }

  /**
   * 处理动作完成
   */
  const handleActionCompleted = () => {
    if (!currentActionType.value) return

    console.log(`[TrainingSession] 动作完成: ${currentActionType.value}`)
    
    // 计算最终得分（取最近几次的平均值）
    const recentScores = scoreHistory.value
      .filter(record => record.actionType === currentActionType.value)
      .slice(-10) // 取最近10次得分
      .map(record => record.score)

    const finalScore = recentScores.length > 0 
      ? Math.round(recentScores.reduce((sum, score) => sum + score, 0) / recentScores.length)
      : realtimeScore.value

    // 重置动作状态
    resetActionState()

    // 触发状态转换
    stateTransition.handleActionComplete(finalScore)
  }

  /**
   * 重置动作状态
   */
  const resetActionState = () => {
    resetActionHoldTimer()
    scoreHistory.value = []
    realtimeScore.value = 0
  }

  /**
   * 获取当前动作反馈
   */
  const getCurrentActionFeedback = () => {
    if (!currentActionType.value) return null
    return actionDetection.getActionFeedback(currentActionType.value, realtimeScore.value)
  }

  /**
   * 获取训练统计信息
   */
  const getTrainingStats = () => {
    return {
      currentScore: realtimeScore.value,
      actionProgress: actionCompletionProgress.value,
      holdTime: actionHoldTime.value,
      requiredHoldTime: HOLD_DURATION,
      isHolding: !!actionStartTime.value,
      feedback: getCurrentActionFeedback()
    }
  }

  /**
   * 手动完成动作（用于测试或特殊情况）
   */
  const manualCompleteAction = (score = null) => {
    const finalScore = score !== null ? score : realtimeScore.value
    console.log(`[TrainingSession] 手动完成动作，得分: ${finalScore}`)
    handleActionCompleted()
  }

  /**
   * 跳过当前动作
   */
  const skipCurrentAction = () => {
    console.log(`[TrainingSession] 跳过动作: ${currentActionType.value}`)
    resetActionState()
    stateTransition.handleActionComplete(0) // 0分完成
  }

  // 监听当前动作变化，重置状态
  watch(
    () => trainingStore.currentAction,
    (newAction, oldAction) => {
      if (newAction && oldAction && newAction.action_id !== oldAction.action_id) {
        console.log(`[TrainingSession] 动作切换: ${oldAction.action_type} -> ${newAction.action_type}`)
        resetActionState()
      }
    }
  )

  // 监听连接状态，自动停止训练
  watch(
    () => connectionStore.isConnected,
    (isConnected) => {
      if (!isConnected && isTrainingActive.value) {
        console.log('[TrainingSession] 连接断开，暂停训练')
        stopTrainingSession()
        notificationStore.showWarning('连接断开，训练已暂停')
      }
    }
  )

  return {
    // 响应式数据
    isTrainingActive,
    realtimeScore,
    actionHoldTime,
    scoreHistory,

    // 计算属性
    currentActionType,
    isActionInProgress,
    actionCompletionProgress,

    // 核心方法
    startTrainingSession,
    stopTrainingSession,
    manualCompleteAction,
    skipCurrentAction,

    // 信息获取
    getCurrentActionFeedback,
    getTrainingStats,

    // 内部方法（暴露用于调试）
    resetActionState
  }
}
