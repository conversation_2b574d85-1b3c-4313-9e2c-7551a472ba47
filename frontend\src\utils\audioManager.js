/**
 * 音频资源管理工具
 * 统一管理音频文件的加载、缓存和播放
 */

// 音频资源配置
export const AUDIO_RESOURCES = {
  // 提示音效
  guidance: {
    url: '/audio/guidance.mp3',
    volume: 0.5,
    category: 'guidance'
  },
  warning: {
    url: '/audio/warning.mp3',
    volume: 0.6,
    category: 'warning'
  },

  // 庆祝音效
  celebration_excellent: {
    url: '/audio/celebration_excellent.mp3',
    volume: 0.8,
    category: 'celebration'
  },
  celebration_good: {
    url: '/audio/celebration_good.mp3',
    volume: 0.7,
    category: 'celebration'
  },
  celebration_fair: {
    url: '/audio/celebration_fair.mp3',
    volume: 0.6,
    category: 'celebration'
  },
  encouragement: {
    url: '/audio/encouragement.mp3',
    volume: 0.6,
    category: 'encouragement'
  },

  // 完成提示
  action_complete: {
    url: '/audio/action_complete.mp3',
    volume: 0.7,
    category: 'completion'
  },
  score_threshold: {
    url: '/audio/score_threshold.mp3',
    volume: 0.6,
    category: 'achievement'
  }
}

// 语音消息配置
export const VOICE_MESSAGES = {
  // 动作指导语音
  guidance: {
    'shoulder_touch_left': '请将左手更靠近右肩',
    'shoulder_touch_right': '请将右手更靠近左肩',
    'arm_raise_left_height': '请将左手举得更高',
    'arm_raise_right_height': '请将右手举得更高',
    'arm_raise_left_straight': '请将左臂伸得更直',
    'arm_raise_right_straight': '请将右臂伸得更直',
    'finger_touch_closer': '请将拇指和食指靠得更近',
    'palm_flip_left_range': '请加大左手翻转幅度',
    'palm_flip_right_range': '请加大右手翻转幅度',
    'frame_incomplete': '请保持关键部位在画面完整展示',
    'stability': '请保持动作稳定，避免抖动'
  },

  // 庆祝语音
  celebration: {
    'excellent': '太棒了！完美完成！',
    'good': '很好！动作完成得很棒！',
    'fair': '不错！继续努力！',
    'poor': '还要加把劲，继续练习！'
  },

  // 鼓励语音
  encouragement: {
    'keep_going': '继续保持，动作不错',
    'almost_there': '快要完成了，坚持住',
    'good_progress': '进步很好，继续努力'
  }
}

/**
 * 音频管理器类
 */
export class AudioManager {
  constructor() {
    this.audioCache = new Map()
    this.isEnabled = true
    this.globalVolume = 0.7
    this.isLoading = false
  }

  /**
   * 预加载音频资源
   */
  async preloadAudio(audioKeys = []) {
    if (this.isLoading) return

    this.isLoading = true
    const loadPromises = []

    const keysToLoad = audioKeys.length > 0 ? audioKeys : Object.keys(AUDIO_RESOURCES)

    for (const key of keysToLoad) {
      const config = AUDIO_RESOURCES[key]
      if (!config) continue

      const loadPromise = new Promise((resolve, reject) => {
        const audio = new Audio(config.url)
        audio.preload = 'auto'
        audio.volume = (config.volume || 0.7) * this.globalVolume

        audio.addEventListener('canplaythrough', () => {
          this.audioCache.set(key, audio)
          resolve(key)
        })

        audio.addEventListener('error', (e) => {
          console.warn(`[AudioManager] 音频加载失败: ${key}`, e)
          reject(e)
        })
      })

      loadPromises.push(loadPromise)
    }

    try {
      await Promise.allSettled(loadPromises)
      console.log(`[AudioManager] 音频预加载完成，已缓存 ${this.audioCache.size} 个音频`)
    } catch (error) {
      console.warn('[AudioManager] 音频预加载出现错误:', error)
    } finally {
      this.isLoading = false
    }
  }

  /**
   * 播放音频
   */
  async playAudio(audioKey, options = {}) {
    if (!this.isEnabled) return false

    try {
      let audio = this.audioCache.get(audioKey)

      // 如果音频未缓存，尝试即时加载
      if (!audio) {
        const config = AUDIO_RESOURCES[audioKey]
        if (!config) {
          console.warn(`[AudioManager] 未找到音频配置: ${audioKey}`)
          return false
        }

        audio = new Audio(config.url)
        audio.volume = (config.volume || 0.7) * this.globalVolume
        this.audioCache.set(audioKey, audio)
      }

      // 设置音量
      if (options.volume !== undefined) {
        audio.volume = options.volume * this.globalVolume
      }

      // 重置播放位置
      audio.currentTime = 0

      // 播放音频
      await audio.play()
      console.log(`[AudioManager] 播放音效: ${audioKey}`)
      return true
    } catch (error) {
      console.warn(`[AudioManager] 音频播放失败: ${audioKey}`, error)
      return false
    }
  }

  /**
   * 播放语音消息
   */
  async playVoiceMessage(messageKey, options = {}) {
    if (!this.isEnabled) return false

    try {
      const message = VOICE_MESSAGES.guidance[messageKey] ||
                     VOICE_MESSAGES.celebration[messageKey] ||
                     VOICE_MESSAGES.encouragement[messageKey]

      if (!message) {
        console.warn(`[AudioManager] 未找到语音消息: ${messageKey}`)
        return false
      }

      // 使用 Web Speech API
      if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(message)
        utterance.lang = 'zh-CN'
        utterance.volume = this.globalVolume * (options.volume || 0.8)
        utterance.rate = options.rate || 1.0
        utterance.pitch = options.pitch || 1.0

        speechSynthesis.speak(utterance)
        console.log(`[AudioManager] 播放语音: ${message}`)
        return true
      } else {
        console.warn('[AudioManager] 浏览器不支持语音合成')
        return false
      }
    } catch (error) {
      console.warn(`[AudioManager] 语音播放失败: ${messageKey}`, error)
      return false
    }
  }

  /**
   * 停止所有音频
   */
  stopAllAudio() {
    // 停止所有缓存的音频
    for (const audio of this.audioCache.values()) {
      audio.pause()
      audio.currentTime = 0
    }

    // 停止语音合成
    if ('speechSynthesis' in window) {
      speechSynthesis.cancel()
    }
  }

  /**
   * 设置全局音量
   */
  setGlobalVolume(volume) {
    this.globalVolume = Math.max(0, Math.min(1, volume))

    // 更新所有缓存音频的音量
    for (const [key, audio] of this.audioCache.entries()) {
      const config = AUDIO_RESOURCES[key]
      if (config) {
        audio.volume = (config.volume || 0.7) * this.globalVolume
      }
    }
  }

  /**
   * 启用/禁用音频
   */
  setEnabled(enabled) {
    this.isEnabled = enabled
    if (!enabled) {
      this.stopAllAudio()
    }
  }

  /**
   * 获取管理器状态
   */
  getStatus() {
    return {
      isEnabled: this.isEnabled,
      globalVolume: this.globalVolume,
      cachedAudioCount: this.audioCache.size,
      isLoading: this.isLoading,
      speechSupported: 'speechSynthesis' in window
    }
  }

  /**
   * 清理资源
   */
  dispose() {
    this.stopAllAudio()
    this.audioCache.clear()
  }
}

// 导出单例实例
export const audioManager = new AudioManager()