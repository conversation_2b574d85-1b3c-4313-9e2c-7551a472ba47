/**
 * 消息通知管理
 * 负责管理系统消息通知和用户反馈
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useNotificationStore = defineStore('notification', () => {
  // 通知相关数据
  const currentNotification = ref(null)
  const notificationHistory = ref([])
  const message = ref('')
  const lastMessageContent = ref('')

  // 计算属性
  const hasActiveNotification = computed(() => {
    return !!currentNotification.value
  })

  const notificationCount = computed(() => {
    return notificationHistory.value.length
  })

  /**
   * 显示通知
   */
  const showNotification = (type, content, duration = 5000) => {
    const notification = {
      id: Date.now(),
      type, // success, warning, error, info
      content,
      timestamp: new Date().toISOString(),
      duration,
      isActive: true
    }

    // 设置当前通知
    currentNotification.value = notification
    
    // 添加到历史记录
    notificationHistory.value.unshift(notification)
    
    // 更新消息内容
    message.value = content
    lastMessageContent.value = content

    console.log(`[Notification] 显示${type}通知: ${content}`)

    // 自动隐藏通知
    if (duration > 0) {
      setTimeout(() => {
        hideNotification(notification.id)
      }, duration)
    }

    return notification.id
  }

  /**
   * 隐藏通知
   */
  const hideNotification = (notificationId = null) => {
    if (notificationId) {
      // 隐藏指定通知
      const notification = notificationHistory.value.find(n => n.id === notificationId)
      if (notification) {
        notification.isActive = false
      }
      
      // 如果是当前通知，清除当前通知
      if (currentNotification.value && currentNotification.value.id === notificationId) {
        currentNotification.value = null
      }
    } else {
      // 隐藏当前通知
      if (currentNotification.value) {
        currentNotification.value.isActive = false
        currentNotification.value = null
      }
    }
  }

  /**
   * 显示成功通知
   */
  const showSuccess = (content, duration = 3000) => {
    return showNotification('success', content, duration)
  }

  /**
   * 显示警告通知
   */
  const showWarning = (content, duration = 5000) => {
    return showNotification('warning', content, duration)
  }

  /**
   * 显示错误通知
   */
  const showError = (content, duration = 8000) => {
    return showNotification('error', content, duration)
  }

  /**
   * 显示信息通知
   */
  const showInfo = (content, duration = 4000) => {
    return showNotification('info', content, duration)
  }

  /**
   * 显示欢迎消息
   */
  const showWelcomeMessage = (patientName) => {
    const welcomeMessage = `欢迎 ${patientName}！系统已为您准备好康复训练计划。`
    return showSuccess(welcomeMessage, 3000)
  }

  /**
   * 显示动作完成消息
   */
  const showActionCompleted = (actionName, score) => {
    const message = `动作"${actionName}"完成！得分：${score}分`
    return showSuccess(message, 2000)
  }

  /**
   * 显示训练完成消息
   */
  const showTrainingCompleted = (totalScore, completionRate) => {
    const message = `恭喜！训练完成。平均得分：${totalScore}分，完成率：${completionRate}%`
    return showSuccess(message, 5000)
  }

  /**
   * 显示连接状态消息
   */
  const showConnectionStatus = (isConnected) => {
    if (isConnected) {
      return showSuccess('系统连接成功，等待用户识别...', 3000)
    } else {
      return showError('系统连接断开，正在尝试重连...', 0) // 不自动隐藏
    }
  }

  /**
   * 清除所有通知
   */
  const clearAllNotifications = () => {
    currentNotification.value = null
    notificationHistory.value = []
    message.value = ''
    lastMessageContent.value = ''
    console.log('[Notification] 清除所有通知')
  }

  /**
   * 清除历史通知（保留当前通知）
   */
  const clearNotificationHistory = () => {
    notificationHistory.value = notificationHistory.value.filter(n => n.isActive)
    console.log('[Notification] 清除历史通知')
  }

  /**
   * 获取最近的通知
   */
  const getRecentNotifications = (count = 5) => {
    return notificationHistory.value.slice(0, count)
  }

  /**
   * 重置通知数据
   */
  const resetNotificationData = () => {
    clearAllNotifications()
  }

  return {
    // 响应式数据
    currentNotification,
    notificationHistory,
    message,
    lastMessageContent,

    // 计算属性
    hasActiveNotification,
    notificationCount,

    // 基础方法
    showNotification,
    hideNotification,
    
    // 便捷方法
    showSuccess,
    showWarning,
    showError,
    showInfo,
    
    // 业务方法
    showWelcomeMessage,
    showActionCompleted,
    showTrainingCompleted,
    showConnectionStatus,
    
    // 管理方法
    clearAllNotifications,
    clearNotificationHistory,
    getRecentNotifications,
    resetNotificationData
  }
})
