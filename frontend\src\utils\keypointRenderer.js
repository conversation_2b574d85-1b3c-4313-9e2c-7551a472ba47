// utils/keypointRenderer.js
export class KeypointRenderer {
  constructor(canvas, options = {}) {
    this.canvas = canvas;
    this.ctx = canvas.getContext("2d");
    this.imageEl = null; // 用于计算偏移
    this.keypoints = [];
    this.smoothedKeypoints = []; // 用于平滑处理
    this.isRendering = false;
    this.animationFrameId = null;

    this.options = {
      pointRadius: 3,
      lineWidth: 2,
      pointColor: "red",
      lineColor: "lime",
      confidenceThreshold: 0.3,
      smoothingFactor: 0.4, // 平滑因子，值越小越平滑但延迟越高
      connections: [],
      ...options,
    };
  }

  updateCanvas(canvas) {
    this.canvas = canvas;
    this.ctx = canvas.getContext("2d");
  }

  updateImageElement(imageEl) {
    this.imageEl = imageEl;
  }

  updateKeypoints(newKeypoints) {
    if (!newKeypoints || newKeypoints.length === 0) {
      this.keypoints = [];
      return;
    }

    // 第一次接收数据或数据格式变化时，直接初始化平滑点
    if (this.smoothedKeypoints.length !== newKeypoints.length) {
      // 使用 structuredClone 进行深拷贝，避免引用问题
      this.smoothedKeypoints = JSON.parse(JSON.stringify(newKeypoints));
    } else {
      // 对每个点进行一阶滞后滤波 (Exponential Smoothing)
      for (let i = 0; i < newKeypoints.length; i++) {
        if (newKeypoints[i] && this.smoothedKeypoints[i]) {
          const prev = this.smoothedKeypoints[i];
          const curr = newKeypoints[i];
          const factor = this.options.smoothingFactor;

          prev[0] = prev[0] * (1 - factor) + curr[0] * factor; // 平滑 x
          prev[1] = prev[1] * (1 - factor) + curr[1] * factor; // 平滑 y
          prev[2] = curr[2]; // 置信度直接使用最新的
        } else {
          // 如果新/旧数据有一个不存在，直接使用新的
           this.smoothedKeypoints[i] = JSON.parse(
             JSON.stringify(newKeypoints[i])
           );
        }
      }
    }
    this.keypoints = this.smoothedKeypoints;
  }

  draw() {
    if (!this.canvas || !this.imageEl) return;

    this.ctx.clearRect(0, 0, this.canvas.clientWidth, this.canvas.clientHeight);

    if (!this.keypoints || this.keypoints.length === 0) return;

    // --- 核心偏移和缩放校正逻辑 ---
    const canvasWidth = this.canvas.clientWidth;
    const canvasHeight = this.canvas.clientHeight;
    const imageNaturalWidth = this.imageEl.naturalWidth;
    const imageNaturalHeight = this.imageEl.naturalHeight;

    if (imageNaturalWidth === 0 || imageNaturalHeight === 0) return;

    const imageAspectRatio = imageNaturalWidth / imageNaturalHeight;
    const canvasAspectRatio = canvasWidth / canvasHeight;

    let renderWidth, renderHeight, xOffset, yOffset;

    if (imageAspectRatio > canvasAspectRatio) {
      renderHeight = canvasHeight;
      renderWidth = canvasHeight * imageAspectRatio;
      xOffset = (canvasWidth - renderWidth) / 2;
      yOffset = 0;
    } else {
      renderWidth = canvasWidth;
      renderHeight = canvasWidth / imageAspectRatio;
      xOffset = 0;
      yOffset = (canvasHeight - renderHeight) / 2;
    }

    // --- 坐标计算 ---
    const calculatedKeypoints = this.keypoints.map((p) => {
      if (p && p.length >= 3 && p[2] > this.options.confidenceThreshold) {
        return {
          x: p[0] * renderWidth + xOffset,
          y: p[1] * renderHeight + yOffset,
        };
      }
      return null;
    });

    // --- 绘制 ---
    // 绘制连接线
    this.ctx.strokeStyle = this.options.lineColor;
    this.ctx.lineWidth = this.options.lineWidth;
    if (this.options.connections) {
      this.options.connections.forEach((pair) => {
        const p1 = calculatedKeypoints[pair[0]];
        const p2 = calculatedKeypoints[pair[1]];
        if (p1 && p2) {
          this.ctx.beginPath();
          this.ctx.moveTo(p1.x, p1.y);
          this.ctx.lineTo(p2.x, p2.y);
          this.ctx.stroke();
        }
      });
    }

    // 绘制关节点
    this.ctx.fillStyle = this.options.pointColor;
    calculatedKeypoints.forEach((point) => {
      if (point) {
        this.ctx.beginPath();
        this.ctx.arc(
          point.x,
          point.y,
          this.options.pointRadius,
          0,
          2 * Math.PI
        );
        this.ctx.fill();
      }
    });
  }

  renderLoop() {
    this.draw();
    this.animationFrameId = requestAnimationFrame(this.renderLoop.bind(this));
  }

  startRenderLoop() {
    if (!this.isRendering) {
      this.isRendering = true;
      this.renderLoop();
    }
  }

  stopRenderLoop() {
    if (this.isRendering) {
      cancelAnimationFrame(this.animationFrameId);
      this.isRendering = false;
    }
  }
}
