import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import './style.css'
import App from './App.vue'
import router from './router'
import { initializeStores } from './stores'
import { runAllTests } from './test-stores'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.use(ElementPlus)

// 应用挂载后初始化stores
app.mount('#app')

// 初始化应用状态
async function initializeApp() {
  try {
    console.log('开始初始化智能康复系统...')

    // 初始化所有stores
    await initializeStores()

    // 在开发环境下运行store测试
    if (import.meta.env.DEV) {
      console.log('🧪 运行Store架构测试...')
      setTimeout(() => {
        runAllTests()
      }, 1000) // 延迟1秒确保所有store都已初始化
    }

    console.log('智能康复系统初始化完成')

  } catch (error) {
    console.error('应用初始化失败:', error)
    // 可以在这里显示错误提示给用户
  }
}

// 启动应用初始化
initializeApp()
