/**
 * WebSocket连接状态管理
 * 负责管理WebSocket连接状态和实时数据接收
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useConnectionStore = defineStore('connection', () => {
  // WebSocket连接状态
  const isConnected = ref(false)
  const connectionError = ref(null)
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = ref(5)

  // 实时数据字段 - 每次接收WebSocket消息都会更新
  const frameData = ref(null)
  const frameCount = ref(0)
  const poseKeypoints = ref([])
  const patientId = ref(null)
  const faceBox = ref(null)

  // 计算属性
  const connectionStatus = computed(() => ({
    isConnected: isConnected.value,
    hasError: !!connectionError.value,
    canReconnect: reconnectAttempts.value < maxReconnectAttempts.value
  }))

  /**
   * 设置连接状态
   */
  const setConnectionStatus = (connected, error = null) => {
    isConnected.value = connected
    connectionError.value = error
    if (connected) {
      reconnectAttempts.value = 0
    }
  }

  /**
   * 增加重连尝试次数
   */
  const incrementReconnectAttempts = () => {
    reconnectAttempts.value++
  }

  /**
   * 重置重连计数
   */
  const resetReconnectAttempts = () => {
    reconnectAttempts.value = 0
  }

  /**
   * 处理实时数据更新
   */
  const updateRealTimeData = (data) => {
    if (!data) return

    // 更新帧数据
    if (data.frame_data) {
      frameData.value = data.frame_data
      frameCount.value += 1
    }

    // 更新姿态关键点数据
    if (data.detect_data) {
      poseKeypoints.value = data.detect_data.pose_keypoints || []
      patientId.value = data.detect_data.patient_id
      faceBox.value = data.detect_data.face_box
    }
  }

  /**
   * 重置所有数据
   */
  const resetData = () => {
    frameData.value = null
    frameCount.value = 0
    poseKeypoints.value = []
    patientId.value = null
    faceBox.value = null
    connectionError.value = null
    reconnectAttempts.value = 0
  }

  return {
    // 响应式数据
    isConnected,
    connectionError,
    reconnectAttempts,
    maxReconnectAttempts,
    frameData,
    frameCount,
    poseKeypoints,
    patientId,
    faceBox,

    // 计算属性
    connectionStatus,

    // 方法
    setConnectionStatus,
    incrementReconnectAttempts,
    resetReconnectAttempts,
    updateRealTimeData,
    resetData
  }
})
