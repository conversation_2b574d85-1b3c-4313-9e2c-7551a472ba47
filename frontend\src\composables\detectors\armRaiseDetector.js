/**
 * 手臂上举动作检测器
 * 实现高度和角度检测逻辑
 */
import { KeyPointMapping } from '@/utils/poseConstants'
import { useActionDetectionEngine } from '../useActionDetectionEngine'

export function useArmRaiseDetector() {
  const engine = useActionDetectionEngine()

  // 动作所需关键点配置
  const REQUIRED_KEYPOINTS = {
    left: [KeyPointMapping.LEFT_WRIST, KeyPointMapping.LEFT_ELBOW, KeyPointMapping.LEFT_SHOULDER, KeyPointMapping.NOSE],
    right: [KeyPointMapping.RIGHT_WRIST, KeyPointMapping.RIGHT_ELBOW, KeyPointMapping.RIGHT_SHOULDER, KeyPointMapping.NOSE]
  }

  // 难度等级配置
  const DIFFICULTY_CONFIG = {
    easy: {
      minHeightRatio: 0.3,       // 最低高度比例（相对肩膀到头顶距离）
      goodHeightRatio: 0.7,      // 良好高度比例
      perfectHeightRatio: 1.0,   // 完美高度比例
      minArmAngle: 120,          // 最小手臂角度
      perfectArmAngle: 160,      // 完美手臂角度
      minScore: 50,              // 最低得分
      maxScore: 100              // 最高得分
    },
    medium: {
      minHeightRatio: 0.5,
      goodHeightRatio: 0.8,
      perfectHeightRatio: 1.1,
      minArmAngle: 140,
      perfectArmAngle: 170,
      minScore: 60,
      maxScore: 100
    },
    hard: {
      minHeightRatio: 0.7,
      goodHeightRatio: 0.9,
      perfectHeightRatio: 1.2,
      minArmAngle: 150,
      perfectArmAngle: 175,
      minScore: 70,
      maxScore: 100
    }
  }

  /**
   * 检测手臂上举动作
   * @param {Array} keypoints - 关键点数组
   * @param {string} side - 动作侧面 ('left' | 'right')
   * @param {string} difficultyLevel - 难度等级
   * @param {number} canvasWidth - 画布宽度
   * @param {number} canvasHeight - 画布高度
   * @returns {Object} - 检测结果
   */
  const detectArmRaise = (keypoints, side = 'left', difficultyLevel = 'easy', canvasWidth = 640, canvasHeight = 480) => {
    const config = DIFFICULTY_CONFIG[difficultyLevel]
    const requiredPoints = REQUIRED_KEYPOINTS[side]

    // 检查关键点完整性
    const completenessCheck = engine.checkRequiredKeypoints(keypoints, requiredPoints, side, canvasWidth, canvasHeight)
    if (!completenessCheck.isComplete) {
      return {
        accuracy: 0,
        stage: 'incomplete',
        feedback: completenessCheck.message,
        heightRatio: 0,
        armAngle: 0
      }
    }

    // 获取关键点
    const wrist = side === 'left' ? 
      keypoints[KeyPointMapping.LEFT_WRIST] : 
      keypoints[KeyPointMapping.RIGHT_WRIST]
    
    const elbow = side === 'left' ? 
      keypoints[KeyPointMapping.LEFT_ELBOW] : 
      keypoints[KeyPointMapping.RIGHT_ELBOW]
    
    const shoulder = side === 'left' ? 
      keypoints[KeyPointMapping.LEFT_SHOULDER] : 
      keypoints[KeyPointMapping.RIGHT_SHOULDER]
    
    const nose = keypoints[KeyPointMapping.NOSE]

    // 计算高度比例
    const heightRatio = calculateHeightRatio(wrist, shoulder, nose)
    
    // 计算手臂角度
    const armAngle = engine.calculateAngle(wrist, elbow, shoulder)

    if (heightRatio === -1) {
      return {
        accuracy: 0,
        stage: 'invalid',
        feedback: '无法计算手臂高度',
        heightRatio: 0,
        armAngle
      }
    }

    // 判断动作阶段和计算得分
    const result = calculateArmRaiseScore(heightRatio, armAngle, config, side)
    
    return {
      ...result,
      heightRatio,
      armAngle
    }
  }

  /**
   * 计算手臂高度比例
   * @param {Object} wrist - 手腕关键点
   * @param {Object} shoulder - 肩膀关键点
   * @param {Object} nose - 鼻子关键点
   * @returns {number} - 高度比例，无法计算返回-1
   */
  const calculateHeightRatio = (wrist, shoulder, nose) => {
    if (!engine.isValidKeypoint(wrist) || !engine.isValidKeypoint(shoulder) || !engine.isValidKeypoint(nose)) {
      return -1
    }

    // 计算参考高度（肩膀到头顶的距离）
    const referenceHeight = Math.abs(shoulder.y - nose.y)
    if (referenceHeight === 0) return -1

    // 计算手腕相对于肩膀的高度
    const wristHeight = shoulder.y - wrist.y // y坐标向下为正，所以用肩膀减去手腕

    // 返回高度比例
    return wristHeight / referenceHeight
  }

  /**
   * 计算手臂上举得分和阶段
   * @param {number} heightRatio - 高度比例
   * @param {number} armAngle - 手臂角度
   * @param {Object} config - 难度配置
   * @param {string} side - 动作侧面
   * @returns {Object} - 得分和反馈
   */
  const calculateArmRaiseScore = (heightRatio, armAngle, config, side) => {
    const sideText = side === 'left' ? '左臂' : '右臂'

    // 计算高度得分
    let heightScore = 0
    let heightStage = 'too_low'

    if (heightRatio >= config.perfectHeightRatio) {
      heightScore = config.maxScore
      heightStage = 'perfect_height'
    } else if (heightRatio >= config.goodHeightRatio) {
      heightScore = config.maxScore - 10
      heightStage = 'good_height'
    } else if (heightRatio >= config.minHeightRatio) {
      heightScore = config.minScore + (heightRatio - config.minHeightRatio) / 
        (config.goodHeightRatio - config.minHeightRatio) * 30
      heightStage = 'raising'
    } else if (heightRatio > 0) {
      heightScore = heightRatio / config.minHeightRatio * config.minScore
      heightStage = 'starting'
    }

    // 计算角度得分
    let angleScore = 0
    if (armAngle >= config.perfectArmAngle) {
      angleScore = 20
    } else if (armAngle >= config.minArmAngle) {
      angleScore = (armAngle - config.minArmAngle) / 
        (config.perfectArmAngle - config.minArmAngle) * 20
    }

    // 综合得分
    const totalScore = Math.round(heightScore * 0.8 + angleScore)
    const accuracy = Math.min(config.maxScore, Math.max(0, totalScore))

    // 生成反馈
    let feedback = ''
    switch (heightStage) {
      case 'too_low':
        feedback = `请将${sideText}抬高`
        break
      case 'starting':
        feedback = `${sideText}开始上举，继续抬高`
        break
      case 'raising':
        feedback = `${sideText}正在上举，继续！`
        break
      case 'good_height':
        feedback = `${sideText}高度很好！`
        break
      case 'perfect_height':
        feedback = `${sideText}高度完美！`
        break
    }

    // 添加角度提示
    if (armAngle < config.minArmAngle && heightRatio >= config.minHeightRatio) {
      feedback += '，请伸直手臂'
    }

    return {
      accuracy,
      stage: heightStage,
      feedback
    }
  }

  /**
   * 检查动作是否完成
   * @param {Object} detectionResult - 检测结果
   * @param {string} difficultyLevel - 难度等级
   * @returns {boolean} - 是否完成
   */
  const isActionCompleted = (detectionResult, difficultyLevel = 'easy') => {
    const config = DIFFICULTY_CONFIG[difficultyLevel]
    return detectionResult.accuracy >= config.minScore + 30 && 
           ['good_height', 'perfect_height'].includes(detectionResult.stage)
  }

  /**
   * 获取动作指导建议
   * @param {Object} detectionResult - 检测结果
   * @param {string} side - 动作侧面
   * @returns {string} - 指导建议
   */
  const getGuidance = (detectionResult, side) => {
    const sideText = side === 'left' ? '左臂' : '右臂'

    switch (detectionResult.stage) {
      case 'incomplete':
        return detectionResult.feedback
      case 'too_low':
        return `缓慢抬起${sideText}，向上举起`
      case 'starting':
        return `继续抬高${sideText}，保持稳定`
      case 'raising':
        return `很好！继续将${sideText}举高`
      case 'good_height':
        return `保持${sideText}的高度，伸直手臂`
      case 'perfect_height':
        return `完美！保持这个姿势`
      default:
        return '请按照示范动作进行'
    }
  }

  return {
    // 主要检测方法
    detectArmRaise,
    isActionCompleted,
    getGuidance,
    
    // 工具方法
    calculateHeightRatio,
    
    // 配置
    REQUIRED_KEYPOINTS,
    DIFFICULTY_CONFIG
  }
}
