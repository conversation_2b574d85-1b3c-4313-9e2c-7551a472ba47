/**
 * 动作检测核心引擎
 * 提供关键点验证、画面完整性检查和基础几何计算工具
 */
import { ref } from 'vue'
import { KeyPointMapping } from '@/utils/poseConstants'

export function useActionDetectionEngine() {
  // 画面完整性状态
  const visibilityStatus = ref({
    isComplete: true,
    missingParts: [],
    message: ''
  })

  /**
   * 检查关键点是否有效
   * @param {Object} point - 关键点对象 {x, y, confidence}
   * @returns {boolean} - 是否有效
   */
  const isValidKeypoint = (point) => {
    if (!point) return false
    
    // 检查坐标是否为无效值 [0,0,0] 或 undefined
    if (point.x === 0 && point.y === 0) return false
    if (point.x === undefined || point.y === undefined) return false
    
    // 检查置信度（如果存在）
    if (point.confidence !== undefined && point.confidence < 0.3) return false
    
    return true
  }

  /**
   * 检查关键点是否在画面边界内
   * @param {Object} point - 关键点对象
   * @param {number} canvasWidth - 画布宽度
   * @param {number} canvasHeight - 画布高度
   * @param {number} margin - 边界容忍度
   * @returns {boolean} - 是否在边界内
   */
  const isPointInBounds = (point, canvasWidth = 640, canvasHeight = 480, margin = 20) => {
    if (!isValidKeypoint(point)) return false
    
    return point.x >= margin && 
           point.x <= canvasWidth - margin && 
           point.y >= margin && 
           point.y <= canvasHeight - margin
  }

  /**
   * 获取身体部位的友好名称
   * @param {number} pointIndex - 关键点索引
   * @param {string} side - 动作侧面 ('left' | 'right')
   * @returns {string} - 部位名称
   */
  const getBodyPartName = (pointIndex, side) => {
    const mapping = {
      [KeyPointMapping.LEFT_WRIST]: '左手',
      [KeyPointMapping.RIGHT_WRIST]: '右手',
      [KeyPointMapping.LEFT_SHOULDER]: '左肩',
      [KeyPointMapping.RIGHT_SHOULDER]: '右肩',
      [KeyPointMapping.LEFT_ELBOW]: '左肘',
      [KeyPointMapping.RIGHT_ELBOW]: '右肘',
      [KeyPointMapping.NOSE]: '头部',
      [KeyPointMapping.LEFT_HAND_WRIST]: '左手腕',
      [KeyPointMapping.RIGHT_HAND_WRIST]: '右手腕',
      [KeyPointMapping.LEFT_HAND_THUMB_4]: '左手拇指',
      [KeyPointMapping.RIGHT_HAND_THUMB_4]: '右手拇指',
      [KeyPointMapping.LEFT_HAND_INDEX_4]: '左手食指',
      [KeyPointMapping.RIGHT_HAND_INDEX_4]: '右手食指'
    }
    
    return mapping[pointIndex] || `${side === 'left' ? '左' : '右'}侧关键部位`
  }

  /**
   * 检查动作所需关键点的完整性
   * @param {Array} keypoints - 关键点数组
   * @param {Array} requiredPoints - 所需关键点索引数组
   * @param {string} side - 动作侧面
   * @param {number} canvasWidth - 画布宽度
   * @param {number} canvasHeight - 画布高度
   * @returns {Object} - 检查结果 {isComplete, missingParts, message}
   */
  const checkRequiredKeypoints = (keypoints, requiredPoints, side, canvasWidth = 640, canvasHeight = 480) => {
    const missingParts = []
    
    for (const pointIndex of requiredPoints) {
      const point = keypoints[pointIndex]
      
      // 检查关键点是否有效且在画面内
      if (!isValidKeypoint(point) || !isPointInBounds(point, canvasWidth, canvasHeight)) {
        const partName = getBodyPartName(pointIndex, side)
        if (!missingParts.includes(partName)) {
          missingParts.push(partName)
        }
      }
    }

    const isComplete = missingParts.length === 0
    let message = ''
    
    if (!isComplete) {
      message = `请保持${missingParts.join('、')}在画面完整展示`
    }

    const result = {
      isComplete,
      missingParts,
      message
    }

    // 更新全局状态
    visibilityStatus.value = result
    
    return result
  }

  /**
   * 计算两点之间的距离
   * @param {Object} point1 - 第一个点
   * @param {Object} point2 - 第二个点
   * @returns {number} - 距离值，无效点返回Infinity
   */
  const calculateDistance = (point1, point2) => {
    if (!isValidKeypoint(point1) || !isValidKeypoint(point2)) {
      return Infinity
    }
    
    const dx = point1.x - point2.x
    const dy = point1.y - point2.y
    return Math.sqrt(dx * dx + dy * dy)
  }

  /**
   * 计算三点形成的角度
   * @param {Object} point1 - 第一个点
   * @param {Object} vertex - 顶点
   * @param {Object} point3 - 第三个点
   * @returns {number} - 角度值（度），无效点返回0
   */
  const calculateAngle = (point1, vertex, point3) => {
    if (!isValidKeypoint(point1) || !isValidKeypoint(vertex) || !isValidKeypoint(point3)) {
      return 0
    }
    
    const vector1 = { x: point1.x - vertex.x, y: point1.y - vertex.y }
    const vector2 = { x: point3.x - vertex.x, y: point3.y - vertex.y }
    
    const dot = vector1.x * vector2.x + vector1.y * vector2.y
    const mag1 = Math.sqrt(vector1.x * vector1.x + vector1.y * vector1.y)
    const mag2 = Math.sqrt(vector2.x * vector2.x + vector2.y * vector2.y)
    
    if (mag1 === 0 || mag2 === 0) return 0
    
    const cosAngle = dot / (mag1 * mag2)
    return Math.acos(Math.max(-1, Math.min(1, cosAngle))) * (180 / Math.PI)
  }

  /**
   * 计算向量的角度（相对于水平线）
   * @param {Object} point1 - 起点
   * @param {Object} point2 - 终点
   * @returns {number} - 角度值（度），无效点返回0
   */
  const calculateVectorAngle = (point1, point2) => {
    if (!isValidKeypoint(point1) || !isValidKeypoint(point2)) {
      return 0
    }
    
    const dx = point2.x - point1.x
    const dy = point2.y - point1.y
    
    return Math.atan2(dy, dx) * (180 / Math.PI)
  }

  /**
   * 标准化距离（基于肩膀宽度）
   * @param {number} distance - 原始距离
   * @param {Array} keypoints - 关键点数组
   * @returns {number} - 标准化距离，无法计算返回Infinity
   */
  const normalizeDistance = (distance, keypoints) => {
    const leftShoulder = keypoints[KeyPointMapping.LEFT_SHOULDER]
    const rightShoulder = keypoints[KeyPointMapping.RIGHT_SHOULDER]
    
    if (!isValidKeypoint(leftShoulder) || !isValidKeypoint(rightShoulder)) {
      return Infinity
    }
    
    const shoulderWidth = calculateDistance(leftShoulder, rightShoulder)
    if (shoulderWidth === 0 || shoulderWidth === Infinity) {
      return Infinity
    }
    
    return distance / shoulderWidth
  }

  return {
    // 响应式数据
    visibilityStatus,
    
    // 关键点验证
    isValidKeypoint,
    isPointInBounds,
    checkRequiredKeypoints,
    
    // 几何计算
    calculateDistance,
    calculateAngle,
    calculateVectorAngle,
    normalizeDistance,
    
    // 工具方法
    getBodyPartName
  }
}
