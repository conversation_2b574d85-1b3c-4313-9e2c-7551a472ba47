/**
 * 状态转换逻辑
 * 管理应用状态的自动转换和流程控制
 */
import { ref, watch } from 'vue'
import { useWorkflowStore } from '@/stores/workflow'
import { usePatientStore } from '@/stores/patient'
import { useTrainingStore } from '@/stores/training'
import { useNotificationStore } from '@/stores/notification'

export function useStateTransition() {
  const workflowStore = useWorkflowStore()
  const patientStore = usePatientStore()
  const trainingStore = useTrainingStore()
  const notificationStore = useNotificationStore()

  // 状态转换控制
  const isAutoTransitionEnabled = ref(true)


  const transitionCallbacks = ref({})

  /**
   * 注册状态转换回调
   */
  const onStateTransition = (fromState, toState, callback) => {
    const key = `${fromState}->${toState}`
    if (!transitionCallbacks.value[key]) {
      transitionCallbacks.value[key] = []
    }
    transitionCallbacks.value[key].push(callback)
  }

  /**
   * 执行状态转换回调
   */
  const executeTransitionCallbacks = (fromState, toState) => {
    const key = `${fromState}->${toState}`
    const callbacks = transitionCallbacks.value[key] || []
    callbacks.forEach(callback => {
      try {
        callback(fromState, toState)
      } catch (error) {
        console.error(`[StateTransition] 回调执行错误 ${key}:`, error)
      }
    })
  }

  /**
   * 监听状态变化并执行相应的转换逻辑
   */
  // watch(
  //   () => workflowStore.currentState,
  //   (newState, oldState) => {
  //     if (oldState && newState !== oldState) {
  //       console.log(`[StateTransition] 状态变化: ${oldState} -> ${newState}`)
  //       executeTransitionCallbacks(oldState, newState)
  //       handleStateTransition(newState, oldState)
  //     }
  //   }
  // )

  /**
   * 处理状态转换的业务逻辑
   */
  const handleStateTransition = (newState, oldState) => {
    switch (newState) {
      case 'waiting':
        handleWaitingState()
        break
      case 'introduction':
        handleIntroductionState()
        break
      case 'preparation':
        handlePreparationState()
        break
      case 'training':
        handleTrainingState()
        break
      case 'reporting':
        handleReportingState()
        break
    }
  }

  /**
   * 处理等待状态
   */
  const handleWaitingState = () => {
    console.log('[StateTransition] 进入等待状态')
    // 重置训练数据
    trainingStore.resetTrainingData()
  }

  /**
   * 处理介绍状态
   */
  const handleIntroductionState = () => {
    console.log('[StateTransition] 进入介绍状态')
    // 初始化动作列表
    trainingStore.initializeActions()
    
    // 开始训练会话
    if (patientStore.userInfo) {
      trainingStore.startTrainingSession(patientStore.userInfo.patient_id)
    }
  }

  /**
   * 处理准备状态
   */
  const handlePreparationState = () => {
    console.log('[StateTransition] 进入准备状态')
    // 准备状态的逻辑由TrainingView组件处理（弹窗播放视频）
  }

  /**
   * 处理训练状态
   */
  const handleTrainingState = () => {
    console.log('[StateTransition] 进入训练状态')
    // 训练状态的逻辑由TrainingView组件处理（动作检测）
  }

  /**
   * 处理报告状态
   */
  const handleReportingState = () => {
    console.log('[StateTransition] 进入报告状态')
    // 生成训练报告
    const report = trainingStore.endTrainingSession()
    
    if (report) {
      notificationStore.showTrainingCompleted(
        report.summary.average_score,
        report.summary.completion_rate
      )
    }
  }
  /**
   * 患者校验成功后的自动转换
   */
  const handlePatientValidationSuccess = () => {
    if (!isAutoTransitionEnabled.value) return;

    console.log("[StateTransition] 患者校验成功，立即转换到介绍页面");
    // 显示欢迎消息
    if (patientStore.userInfo) {
      notificationStore.showWelcomeMessage(patientStore.userInfo.patient_name);
    }

    console.log("handlePatientValidationSuccess");
    // 立即转换到介绍页面，不使用延迟
    workflowStore.scheduleIntroductionTransition("introduction");

  }

  /**
   * 介绍页面倒计时结束后的转换
   */
  const handleIntroductionComplete = () => {
    if (!isAutoTransitionEnabled.value) return

    console.log('[StateTransition] 介绍页面完成，转换到准备状态')
    workflowStore.transitionToPreparation()
  }

  /**
   * 动作介绍视频播放完成后的转换
   */
  const handleActionIntroComplete = () => {
    if (!isAutoTransitionEnabled.value) return

    console.log('[StateTransition] 动作介绍完成，转换到训练状态')
    workflowStore.transitionToTraining()
  }

  /**
   * 动作完成后的转换
   */
  const handleActionComplete = (actionScore) => {
    if (!isAutoTransitionEnabled.value) return

    console.log(`[StateTransition] 动作完成，得分: ${actionScore}`)
    
    // 完成当前动作
    trainingStore.completeCurrentAction(actionScore)
    
    // 显示动作完成消息
    if (trainingStore.currentAction) {
      notificationStore.showActionCompleted(
        trainingStore.currentAction.action_name,
        actionScore
      )
    }

    // 检查是否还有下一个动作
    if (trainingStore.isAllActionsCompleted) {
      // 所有动作完成，转换到报告状态
      setTimeout(() => {
        workflowStore.transitionToReporting()
      }, 2000) // 2秒后转换，让用户看到完成消息
    } else {
      // 还有下一个动作，切换到下一个动作并转换到准备状态
      setTimeout(() => {
        const hasNext = trainingStore.moveToNextAction()
        if (hasNext) {
          workflowStore.transitionToNextActionPreparation()
        } else {
          workflowStore.transitionToReporting()
        }
      }, 2000)
    }
  }

  /**
   * 启用自动转换
   */
  const enableAutoTransition = () => {
    isAutoTransitionEnabled.value = true
    console.log('[StateTransition] 启用自动状态转换')
  }

  /**
   * 禁用自动转换
   */
  const disableAutoTransition = () => {
    isAutoTransitionEnabled.value = false
    console.log('[StateTransition] 禁用自动状态转换')
  }

  /**
   * 手动触发状态转换
   */
  const manualTransition = (targetState, delay = 0) => {
    console.log(`[StateTransition] 手动转换到状态: ${targetState}`)
    return workflowStore.transitionTo(targetState, delay)
  }

  /**
   * 重置状态转换系统
   */
  const resetStateTransition = () => {
    workflowStore.resetWorkflow()
    transitionCallbacks.value = {}
    isAutoTransitionEnabled.value = true
    console.log('[StateTransition] 状态转换系统重置')
  }

  // // 监听患者校验成功事件
  // watch(
  //   () => patientStore.userInfo,
  //   (newUserInfo, oldUserInfo) => {
  //     if (newUserInfo && !oldUserInfo && workflowStore.currentState === 'waiting') {
  //       handlePatientValidationSuccess()
  //     }
  //   }
  // )

  return {
    // 响应式数据
    isAutoTransitionEnabled,

    // 核心方法
    onStateTransition,
    handlePatientValidationSuccess,
    handleIntroductionComplete,
    handleActionIntroComplete,
    handleActionComplete,

    // 控制方法
    enableAutoTransition,
    disableAutoTransition,
    manualTransition,
    resetStateTransition
  }
}
