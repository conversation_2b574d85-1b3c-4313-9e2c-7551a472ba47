<template>
  <!-- router-view 将会显示当前路由对应的组件 -->
  <router-view v-slot="{ Component }">
    <!-- 可选：添加过渡效果以获得更流畅的用户体验 -->
    <transition name="fade" mode="out-in">
      <component :is="Component" />
    </transition>
  </router-view>
  <!-- 您也可以在这里放置全局UI元素，比如您的通知组件 -->
</template>

<script setup>
import { watch } from 'vue';
import { useWorkflowStore } from '@/stores/workflow';
import { useRouter, useRoute } from 'vue-router';

const workflowStore = useWorkflowStore();
const router = useRouter();
const route = useRoute(); // 获取当前路由对象，用于判断是否需要跳转

// 这就是我们的中央状态机路由控制器！
watch(
  () => workflowStore.currentState,
  (newState) => {
    console.log(`[App.vue 监听器] 全局状态变更为: ${newState}`)
    // 根据新的状态，获取目标路由的名称
    const targetRouteName = getRouteNameForState(newState);
    // 只有当我们不在目标路由上时，才执行跳转
    if (targetRouteName && route.name !== targetRouteName) {
      console.log(`正在导航至路由: '${targetRouteName}'`);
      router.push({ name: targetRouteName });
    }
  }
);

/**
 * 一个辅助函数，用于将 store 的状态映射到路由名称。
 * 这能让逻辑保持清晰且易于管理。
 * 请确保您在 `router/index.js` 中定义的路由名称与这些字符串匹配。
 */
function getRouteNameForState(state) {
  switch (state) {
    case 'start_failed':
      return 'Error'; // 假设您的错误路由命名为 'Error'
    
    case 'waiting':
    case 'login_success': // LoginView 自身可以处理欢迎动画
      return 'Login'; // 您的登录路由的 name

    case 'introduction': // 用于介绍任务的状态
      return 'TaskIntroduction'; // 您的任务介绍/时钟页面的 name

    case 'preparation': // 用于动作引导视频的状态
    case 'training':
      return 'Training'; // 您的训练页面的 name

    case 'reporting':
      return 'Report'; // 您的报告页面的 name
      
    // 对于像 'pause' 这样的状态，我们不希望改变路由。
    // 当前的视图组件应该自己显示一个暂停的遮罩层。
    case 'pause':
      return null; 

    default:
      console.warn(`未找到与状态 '${state}' 匹配的路由`);
      return null; // 对于未处理的状态，不执行任何操作
  }
}
</script>

<style>
/* 可选的 router-view 淡入淡出过渡效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
