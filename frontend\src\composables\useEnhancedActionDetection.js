/**
 * 增强版动作检测逻辑
 * 实现多维度评分、画面完整性检测和智能反馈
 */
import { ref, computed } from 'vue'
import { KeyPointMapping } from '@/utils/poseConstants'

export function useEnhancedActionDetection() {
  // 检测状态
  const isDetecting = ref(false)
  const detectionHistory = ref([])
  const currentScores = ref({
    overall: 0,
    accuracy: 0,
    stability: 0,
    completeness: 0
  })
  
  // 画面完整性状态
  const visibilityStatus = ref({
    isComplete: true,
    missingParts: [],
    message: ''
  })

  // 难度等级配置
  const DIFFICULTY_CONFIG = {
    easy: {
      angleDeviation: 15,
      distanceDeviation: 0.3,
      stabilityThreshold: 0.8,
      completionThreshold: 75,
      stabilityWindow: 10 // 稳定性计算窗口帧数
    },
    medium: {
      angleDeviation: 10,
      distanceDeviation: 0.2,
      stabilityThreshold: 0.85,
      completionThreshold: 80,
      stabilityWindow: 15
    },
    hard: {
      angleDeviation: 5,
      distanceDeviation: 0.1,
      stabilityThreshold: 0.9,
      completionThreshold: 85,
      stabilityWindow: 20
    }
  }

  // 动作所需关键点配置
  const ACTION_REQUIRED_KEYPOINTS = {
    shoulder_touch: {
      left: [KeyPointMapping.LEFT_WRIST, KeyPointMapping.RIGHT_SHOULDER, KeyPointMapping.LEFT_SHOULDER],
      right: [KeyPointMapping.RIGHT_WRIST, KeyPointMapping.LEFT_SHOULDER, KeyPointMapping.RIGHT_SHOULDER]
    },
    arm_raise: {
      left: [KeyPointMapping.LEFT_WRIST, KeyPointMapping.LEFT_ELBOW, KeyPointMapping.LEFT_SHOULDER, KeyPointMapping.NOSE],
      right: [KeyPointMapping.RIGHT_WRIST, KeyPointMapping.RIGHT_ELBOW, KeyPointMapping.RIGHT_SHOULDER, KeyPointMapping.NOSE]
    },
    finger_touch: {
      left: [KeyPointMapping.LEFT_HAND_WRIST, KeyPointMapping.LEFT_HAND_THUMB_4, KeyPointMapping.LEFT_HAND_INDEX_4],
      right: [KeyPointMapping.RIGHT_HAND_WRIST, KeyPointMapping.RIGHT_HAND_THUMB_4, KeyPointMapping.RIGHT_HAND_INDEX_4]
    },
    palm_flip: {
      left: [KeyPointMapping.LEFT_WRIST, KeyPointMapping.LEFT_ELBOW, KeyPointMapping.LEFT_HAND_WRIST],
      right: [KeyPointMapping.RIGHT_WRIST, KeyPointMapping.RIGHT_ELBOW, KeyPointMapping.RIGHT_HAND_WRIST]
    }
  }

  /**
   * 计算两点之间的距离
   */
  const calculateDistance = (point1, point2) => {
    if (!point1 || !point2 || !point1.x || !point1.y || !point2.x || !point2.y) {
      return Infinity
    }
    const dx = point1.x - point2.x
    const dy = point1.y - point2.y
    return Math.sqrt(dx * dx + dy * dy)
  }

  /**
   * 计算角度
   */
  const calculateAngle = (point1, point2, point3) => {
    if (!point1 || !point2 || !point3) return 0
    
    const vector1 = { x: point1.x - point2.x, y: point1.y - point2.y }
    const vector2 = { x: point3.x - point2.x, y: point3.y - point2.y }
    
    const dot = vector1.x * vector2.x + vector1.y * vector2.y
    const mag1 = Math.sqrt(vector1.x * vector1.x + vector1.y * vector1.y)
    const mag2 = Math.sqrt(vector2.x * vector2.x + vector2.y * vector2.y)
    
    if (mag1 === 0 || mag2 === 0) return 0
    
    const cosAngle = dot / (mag1 * mag2)
    return Math.acos(Math.max(-1, Math.min(1, cosAngle))) * (180 / Math.PI)
  }

  /**
   * 检查画面完整性
   */
  const checkFrameCompleteness = (keypoints, actionType, side, canvasWidth = 640, canvasHeight = 480) => {
    const requiredPoints = ACTION_REQUIRED_KEYPOINTS[actionType]?.[side] || []
    const missingParts = []
    const margin = 20 // 边界容忍度

    for (const pointIndex of requiredPoints) {
      const point = keypoints[pointIndex]
      if (!point || point.x < margin || point.x > canvasWidth - margin || 
          point.y < margin || point.y > canvasHeight - margin) {
        
        // 根据关键点索引生成友好的部位名称
        const partName = getBodyPartName(pointIndex, side)
        if (partName && !missingParts.includes(partName)) {
          missingParts.push(partName)
        }
      }
    }

    const isComplete = missingParts.length === 0
    let message = ''
    
    if (!isComplete) {
      message = `请保持${missingParts.join('、')}在画面完整展示`
    }

    visibilityStatus.value = {
      isComplete,
      missingParts,
      message
    }

    return isComplete
  }

  /**
   * 获取身体部位友好名称
   */
  const getBodyPartName = (pointIndex, side) => {
    const sideText = side === 'left' ? '左' : '右'
    const mapping = {
      [KeyPointMapping.LEFT_WRIST]: '左手',
      [KeyPointMapping.RIGHT_WRIST]: '右手',
      [KeyPointMapping.LEFT_SHOULDER]: '左肩',
      [KeyPointMapping.RIGHT_SHOULDER]: '右肩',
      [KeyPointMapping.LEFT_ELBOW]: '左肘',
      [KeyPointMapping.RIGHT_ELBOW]: '右肘',
      [KeyPointMapping.NOSE]: '头部',
      [KeyPointMapping.LEFT_HAND_WRIST]: '左手腕',
      [KeyPointMapping.RIGHT_HAND_WRIST]: '右手腕'
    }
    return mapping[pointIndex] || `${sideText}侧关键部位`
  }

  /**
   * 计算稳定性得分
   */
  const calculateStability = (actionType, currentData) => {
    const config = DIFFICULTY_CONFIG.easy // 默认使用easy配置
    const windowSize = config.stabilityWindow

    if (detectionHistory.value.length < windowSize) return 100

    const recentScores = detectionHistory.value
      .slice(-windowSize)
      .map(record => record.accuracy)

    if (recentScores.length < 2) return 100

    // 计算分数方差
    const mean = recentScores.reduce((sum, score) => sum + score, 0) / recentScores.length
    const variance = recentScores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / recentScores.length
    const standardDeviation = Math.sqrt(variance)

    // 稳定性得分：标准差越小，稳定性越高
    const stabilityScore = Math.max(0, 100 - (standardDeviation * 2))
    return Math.round(stabilityScore)
  }

  /**
   * 增强版肩部触摸检测
   */
  const detectEnhancedShoulderTouch = (keypoints, side, difficultyLevel = 'easy') => {
    const config = DIFFICULTY_CONFIG[difficultyLevel]

    try {
      const leftWrist = keypoints[KeyPointMapping.LEFT_WRIST]
      const rightWrist = keypoints[KeyPointMapping.RIGHT_WRIST]
      const leftShoulder = keypoints[KeyPointMapping.LEFT_SHOULDER]
      const rightShoulder = keypoints[KeyPointMapping.RIGHT_SHOULDER]

      if (!leftWrist || !rightWrist || !leftShoulder || !rightShoulder) {
        return { accuracy: 0, feedback: '无法检测到关键身体部位' }
      }

      let targetDistance, referenceDistance, feedback = ''

      if (side === 'left') {
        // 左手触右肩
        targetDistance = calculateDistance(leftWrist, rightShoulder)
        referenceDistance = calculateDistance(leftShoulder, rightShoulder) * 0.3 // 期望距离
        feedback = targetDistance > referenceDistance * 1.5 ? '请将左手更靠近右肩' : '动作很好，保持住'
      } else {
        // 右手触左肩
        targetDistance = calculateDistance(rightWrist, leftShoulder)
        referenceDistance = calculateDistance(leftShoulder, rightShoulder) * 0.3
        feedback = targetDistance > referenceDistance * 1.5 ? '请将右手更靠近左肩' : '动作很好，保持住'
      }

      // 计算准确性得分
      const distanceRatio = targetDistance / referenceDistance
      const accuracyScore = Math.max(0, 100 - (distanceRatio - 1) * 100 / config.distanceDeviation)

      return {
        accuracy: Math.round(Math.min(100, accuracyScore)),
        feedback,
        targetDistance,
        referenceDistance
      }
    } catch (error) {
      console.warn('[EnhancedActionDetection] 肩部触摸检测错误:', error)
      return { accuracy: 0, feedback: '检测出现错误' }
    }
  }

  /**
   * 增强版手臂上举检测
   */
  const detectEnhancedArmRaise = (keypoints, side, difficultyLevel = 'easy') => {
    const config = DIFFICULTY_CONFIG[difficultyLevel]

    try {
      const wrist = side === 'left' ? keypoints[KeyPointMapping.LEFT_WRIST] : keypoints[KeyPointMapping.RIGHT_WRIST]
      const elbow = side === 'left' ? keypoints[KeyPointMapping.LEFT_ELBOW] : keypoints[KeyPointMapping.RIGHT_ELBOW]
      const shoulder = side === 'left' ? keypoints[KeyPointMapping.LEFT_SHOULDER] : keypoints[KeyPointMapping.RIGHT_SHOULDER]
      const nose = keypoints[KeyPointMapping.NOSE]

      if (!wrist || !elbow || !shoulder || !nose) {
        return { accuracy: 0, feedback: '无法检测到关键身体部位' }
      }

      // 计算手腕高度（相对于头部）
      const wristHeight = nose.y - wrist.y
      const shoulderToHead = Math.abs(nose.y - shoulder.y)

      if (shoulderToHead === 0) return { accuracy: 0, feedback: '检测基准点异常' }

      // 计算手臂角度
      const armAngle = calculateAngle(wrist, elbow, shoulder)

      // 高度得分
      const heightRatio = wristHeight / shoulderToHead
      const heightScore = Math.max(0, Math.min(100, heightRatio * 100))

      // 角度得分（期望角度接近180度，即手臂伸直）
      const angleDeviation = Math.abs(180 - armAngle)
      const angleScore = Math.max(0, 100 - (angleDeviation / config.angleDeviation) * 100)

      // 综合准确性得分
      const accuracyScore = (heightScore * 0.7 + angleScore * 0.3)

      let feedback = ''
      if (heightScore < 60) {
        feedback = `请将${side === 'left' ? '左' : '右'}手举得更高`
      } else if (angleScore < 60) {
        feedback = `请将${side === 'left' ? '左' : '右'}臂伸得更直`
      } else {
        feedback = '动作很好，保持住'
      }

      return {
        accuracy: Math.round(accuracyScore),
        feedback,
        heightScore,
        angleScore,
        armAngle
      }
    } catch (error) {
      console.warn('[EnhancedActionDetection] 手臂上举检测错误:', error)
      return { accuracy: 0, feedback: '检测出现错误' }
    }
  }

  /**
   * 增强版指尖对触检测
   */
  const detectEnhancedFingerTouch = (keypoints, side, difficultyLevel = 'easy') => {
    const config = DIFFICULTY_CONFIG[difficultyLevel]

    try {
      // 使用手部关键点进行精确检测
      const thumbTip = side === 'left' ? keypoints[KeyPointMapping.LEFT_HAND_THUMB_4] : keypoints[KeyPointMapping.RIGHT_HAND_THUMB_4]
      const indexTip = side === 'left' ? keypoints[KeyPointMapping.LEFT_HAND_INDEX_4] : keypoints[KeyPointMapping.RIGHT_HAND_INDEX_4]
      const wrist = side === 'left' ? keypoints[KeyPointMapping.LEFT_HAND_WRIST] : keypoints[KeyPointMapping.RIGHT_HAND_WRIST]

      if (!thumbTip || !indexTip || !wrist) {
        // 降级到手腕检测
        const leftWrist = keypoints[KeyPointMapping.LEFT_WRIST]
        const rightWrist = keypoints[KeyPointMapping.RIGHT_WRIST]

        if (!leftWrist || !rightWrist) {
          return { accuracy: 0, feedback: '无法检测到手部关键点' }
        }

        const handsDistance = calculateDistance(leftWrist, rightWrist)
        const shoulderWidth = calculateDistance(keypoints[KeyPointMapping.LEFT_SHOULDER], keypoints[KeyPointMapping.RIGHT_SHOULDER])
        const expectedDistance = shoulderWidth * 0.1 // 期望距离

        const accuracyScore = Math.max(0, 100 - (handsDistance / expectedDistance - 1) * 100 / config.distanceDeviation)

        return {
          accuracy: Math.round(accuracyScore),
          feedback: handsDistance > expectedDistance * 1.5 ? '请将双手靠得更近' : '动作很好，保持住'
        }
      }

      // 精确的指尖距离检测
      const fingerDistance = calculateDistance(thumbTip, indexTip)
      const handSize = calculateDistance(thumbTip, wrist) // 估算手掌大小
      const expectedDistance = handSize * 0.1 // 期望指尖几乎接触

      const accuracyScore = Math.max(0, 100 - (fingerDistance / expectedDistance - 1) * 100 / config.distanceDeviation)

      let feedback = ''
      if (fingerDistance > expectedDistance * 2) {
        feedback = '请将拇指和食指靠得更近'
      } else if (fingerDistance < expectedDistance * 0.5) {
        feedback = '很好！指尖接触很准确'
      } else {
        feedback = '动作很好，保持住'
      }

      return {
        accuracy: Math.round(accuracyScore),
        feedback,
        fingerDistance,
        expectedDistance
      }
    } catch (error) {
      console.warn('[EnhancedActionDetection] 指尖对触检测错误:', error)
      return { accuracy: 0, feedback: '检测出现错误' }
    }
  }

  /**
   * 增强版手掌翻转检测
   */
  const detectEnhancedPalmFlip = (keypoints, side, difficultyLevel = 'easy') => {
    try {
      const wrist = side === 'left' ? keypoints[KeyPointMapping.LEFT_WRIST] : keypoints[KeyPointMapping.RIGHT_WRIST]
      const elbow = side === 'left' ? keypoints[KeyPointMapping.LEFT_ELBOW] : keypoints[KeyPointMapping.RIGHT_ELBOW]
      const handWrist = side === 'left' ? keypoints[KeyPointMapping.LEFT_HAND_WRIST] : keypoints[KeyPointMapping.RIGHT_HAND_WRIST]

      if (!wrist || !elbow) {
        return { accuracy: 0, feedback: '无法检测到手臂关键点' }
      }

      // 计算前臂角度
      const forearmAngle = Math.atan2(wrist.y - elbow.y, wrist.x - elbow.x) * (180 / Math.PI)

      // 检测翻转动作（基于角度变化）
      const recentAngles = detectionHistory.value
        .filter(record => record.actionType === 'palm_flip')
        .slice(-10)
        .map(record => record.forearmAngle)

      let flipScore = 50 // 基础分数

      if (recentAngles.length >= 3) {
        // 检测角度变化范围
        const maxAngle = Math.max(...recentAngles)
        const minAngle = Math.min(...recentAngles)
        const angleRange = maxAngle - minAngle

        // 翻转幅度越大得分越高
        flipScore = Math.min(100, 50 + (angleRange / 90) * 50)
      }

      let feedback = ''
      if (flipScore < 60) {
        feedback = `请加大${side === 'left' ? '左' : '右'}手翻转幅度`
      } else if (flipScore < 80) {
        feedback = '翻转动作不错，可以更快一些'
      } else {
        feedback = '翻转动作很好，保持节奏'
      }

      return {
        accuracy: Math.round(flipScore),
        feedback,
        forearmAngle,
        angleRange: recentAngles.length >= 3 ? Math.max(...recentAngles) - Math.min(...recentAngles) : 0
      }
    } catch (error) {
      console.warn('[EnhancedActionDetection] 手掌翻转检测错误:', error)
      return { accuracy: 0, feedback: '检测出现错误' }
    }
  }

  /**
   * 主要检测函数 - 多维度评分
   */
  const detectActionEnhanced = (actionType, keypoints, side = 'left', difficultyLevel = 'easy', canvasWidth = 640, canvasHeight = 480) => {
    if (!actionType || !keypoints) {
      return {
        overall: 0,
        accuracy: 0,
        stability: 0,
        completeness: 0,
        feedback: '无效的检测参数'
      }
    }

    // 检查画面完整性
    const isFrameComplete = checkFrameCompleteness(keypoints, actionType, side, canvasWidth, canvasHeight)
    const completenessScore = isFrameComplete ? 100 : 0

    // 如果画面不完整，返回低分和提示
    if (!isFrameComplete) {
      currentScores.value = {
        overall: 0,
        accuracy: 0,
        stability: 0,
        completeness: completenessScore
      }

      return {
        ...currentScores.value,
        feedback: visibilityStatus.value.message
      }
    }

    // 根据动作类型进行检测
    let detectionResult = { accuracy: 0, feedback: '未知动作类型' }

    switch (actionType) {
      case 'shoulder_touch':
        detectionResult = detectEnhancedShoulderTouch(keypoints, side, difficultyLevel)
        break
      case 'arm_raise':
        detectionResult = detectEnhancedArmRaise(keypoints, side, difficultyLevel)
        break
      case 'finger_touch':
        detectionResult = detectEnhancedFingerTouch(keypoints, side, difficultyLevel)
        break
      case 'palm_flip':
        detectionResult = detectEnhancedPalmFlip(keypoints, side, difficultyLevel)
        break
    }

    // 计算稳定性
    const stabilityScore = calculateStability(actionType, detectionResult)

    // 计算综合得分
    const overallScore = Math.round(
      detectionResult.accuracy * 0.5 +
      stabilityScore * 0.3 +
      completenessScore * 0.2
    )

    // 更新当前分数
    currentScores.value = {
      overall: overallScore,
      accuracy: detectionResult.accuracy,
      stability: stabilityScore,
      completeness: completenessScore
    }

    // 记录检测历史
    detectionHistory.value.push({
      timestamp: Date.now(),
      actionType,
      side,
      accuracy: detectionResult.accuracy,
      stability: stabilityScore,
      completeness: completenessScore,
      overall: overallScore,
      forearmAngle: detectionResult.forearmAngle || 0,
      keypoints: keypoints.length
    })

    // 保持历史记录在合理范围内
    if (detectionHistory.value.length > 100) {
      detectionHistory.value = detectionHistory.value.slice(-50)
    }

    return {
      ...currentScores.value,
      feedback: detectionResult.feedback || '继续保持'
    }
  }

  return {
    // 响应式数据
    isDetecting,
    detectionHistory,
    currentScores,
    visibilityStatus,

    // 主要检测方法
    detectActionEnhanced,

    // 单独检测方法
    detectEnhancedShoulderTouch,
    detectEnhancedArmRaise,
    detectEnhancedFingerTouch,
    detectEnhancedPalmFlip,
    calculateStability,

    // 工具方法
    calculateDistance,
    calculateAngle,
    checkFrameCompleteness,
    getBodyPartName
  }
}
