/**
 * 声音反馈系统
 * 处理提示音和庆祝音效播放
 */
import { ref } from 'vue'

export function useAudioFeedback() {
  // 音频状态
  const isAudioEnabled = ref(true)
  const currentVolume = ref(0.7)
  const audioQueue = ref([])
  const isPlaying = ref(false)

  // 音频资源配置
  const AUDIO_CONFIG = {
    // 提示音
    guidance: {
      url: '/audio/guidance.mp3',
      volume: 0.5,
      type: 'guidance'
    },
    warning: {
      url: '/audio/warning.mp3',
      volume: 0.6,
      type: 'warning'
    },
    
    // 庆祝音效
    celebration_excellent: {
      url: '/audio/celebration_excellent.mp3',
      volume: 0.8,
      type: 'celebration'
    },
    celebration_good: {
      url: '/audio/celebration_good.mp3',
      volume: 0.7,
      type: 'celebration'
    },
    celebration_fair: {
      url: '/audio/celebration_fair.mp3',
      volume: 0.6,
      type: 'celebration'
    },
    encouragement: {
      url: '/audio/encouragement.mp3',
      volume: 0.6,
      type: 'encouragement'
    },

    // 动作完成提示
    action_complete: {
      url: '/audio/action_complete.mp3',
      volume: 0.7,
      type: 'completion'
    },

    // 分数达标提示
    score_threshold: {
      url: '/audio/score_threshold.mp3',
      volume: 0.6,
      type: 'achievement'
    }
  }

  // 语音提示配置
  const VOICE_MESSAGES = {
    // 动作指导
    guidance: {
      'shoulder_touch_left': '请将左手更靠近右肩',
      'shoulder_touch_right': '请将右手更靠近左肩',
      'arm_raise_left_height': '请将左手举得更高',
      'arm_raise_right_height': '请将右手举得更高',
      'arm_raise_left_straight': '请将左臂伸得更直',
      'arm_raise_right_straight': '请将右臂伸得更直',
      'finger_touch_closer': '请将拇指和食指靠得更近',
      'palm_flip_left_range': '请加大左手翻转幅度',
      'palm_flip_right_range': '请加大右手翻转幅度',
      'frame_incomplete': '请保持关键部位在画面完整展示',
      'stability': '请保持动作稳定，避免抖动'
    },

    // 庆祝信息
    celebration: {
      'excellent': '太棒了！完美完成！',
      'good': '很好！动作完成得很棒！',
      'fair': '不错！继续努力！',
      'poor': '还要加把劲，继续练习！'
    },

    // 鼓励信息
    encouragement: {
      'keep_going': '继续保持，动作不错',
      'almost_there': '快要完成了，坚持住',
      'good_progress': '进步很好，继续努力'
    }
  }

  /**
   * 创建音频实例
   */
  const createAudio = (config) => {
    const audio = new Audio(config.url)
    audio.volume = (config.volume || 0.7) * currentVolume.value
    audio.preload = 'auto'
    return audio
  }

  /**
   * 播放音效
   */
  const playAudio = async (audioType, options = {}) => {
    if (!isAudioEnabled.value) return false

    try {
      const config = AUDIO_CONFIG[audioType]
      if (!config) {
        console.warn(`[AudioFeedback] 未找到音频配置: ${audioType}`)
        return false
      }

      const audio = createAudio(config)
      
      // 设置音量
      if (options.volume !== undefined) {
        audio.volume = options.volume * currentVolume.value
      }

      // 播放音频
      await audio.play()
      
      console.log(`[AudioFeedback] 播放音效: ${audioType}`)
      return true
    } catch (error) {
      console.warn(`[AudioFeedback] 音频播放失败: ${audioType}`, error)
      return false
    }
  }

  /**
   * 播放语音提示
   */
  const playVoiceMessage = async (messageKey, options = {}) => {
    if (!isAudioEnabled.value) return false

    try {
      const message = VOICE_MESSAGES.guidance[messageKey] || 
                     VOICE_MESSAGES.celebration[messageKey] || 
                     VOICE_MESSAGES.encouragement[messageKey]

      if (!message) {
        console.warn(`[AudioFeedback] 未找到语音消息: ${messageKey}`)
        return false
      }

      // 使用 Web Speech API 进行语音合成
      if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(message)
        utterance.lang = 'zh-CN'
        utterance.volume = currentVolume.value * (options.volume || 0.8)
        utterance.rate = options.rate || 1.0
        utterance.pitch = options.pitch || 1.0

        speechSynthesis.speak(utterance)
        console.log(`[AudioFeedback] 播放语音: ${message}`)
        return true
      } else {
        console.warn('[AudioFeedback] 浏览器不支持语音合成')
        return false
      }
    } catch (error) {
      console.warn(`[AudioFeedback] 语音播放失败: ${messageKey}`, error)
      return false
    }
  }

  /**
   * 播放动作指导音效
   */
  const playGuidanceAudio = async (actionType, side, issueType, options = {}) => {
    // 先播放提示音
    await playAudio('guidance', { volume: 0.4 })
    
    // 延迟播放语音指导
    setTimeout(() => {
      const messageKey = `${actionType}_${side}_${issueType}` || `${actionType}_${issueType}` || issueType
      playVoiceMessage(messageKey, options)
    }, 300)
  }

  /**
   * 播放庆祝音效
   */
  const playCelebrationAudio = async (level, finalScore) => {
    const audioType = `celebration_${level}`
    
    // 播放庆祝音效
    await playAudio(audioType)
    
    // 延迟播放语音庆祝
    setTimeout(() => {
      playVoiceMessage(level, { volume: 0.9 })
    }, 500)
  }

  /**
   * 播放分数达标提示
   */
  const playScoreThresholdAudio = async (score) => {
    await playAudio('score_threshold')
    
    setTimeout(() => {
      if (score >= 85) {
        playVoiceMessage('good_progress')
      } else {
        playVoiceMessage('almost_there')
      }
    }, 400)
  }

  /**
   * 播放画面不完整警告
   */
  const playFrameWarningAudio = async () => {
    await playAudio('warning', { volume: 0.5 })
    
    setTimeout(() => {
      playVoiceMessage('frame_incomplete')
    }, 200)
  }

  /**
   * 播放动作完成音效
   */
  const playActionCompleteAudio = async () => {
    await playAudio('action_complete')
  }

  /**
   * 停止所有音频
   */
  const stopAllAudio = () => {
    // 停止语音合成
    if ('speechSynthesis' in window) {
      speechSynthesis.cancel()
    }
    
    // 清空音频队列
    audioQueue.value = []
    isPlaying.value = false
  }

  /**
   * 设置音频开关
   */
  const setAudioEnabled = (enabled) => {
    isAudioEnabled.value = enabled
    if (!enabled) {
      stopAllAudio()
    }
  }

  /**
   * 设置音量
   */
  const setVolume = (volume) => {
    currentVolume.value = Math.max(0, Math.min(1, volume))
  }

  /**
   * 测试音频功能
   */
  const testAudio = async () => {
    console.log('[AudioFeedback] 测试音频功能...')
    
    // 测试提示音
    await playAudio('guidance')
    
    // 测试语音
    setTimeout(() => {
      playVoiceMessage('keep_going')
    }, 1000)
    
    // 测试庆祝音效
    setTimeout(() => {
      playCelebrationAudio('good', 85)
    }, 3000)
  }

  /**
   * 获取音频状态
   */
  const getAudioStatus = () => {
    return {
      isEnabled: isAudioEnabled.value,
      volume: currentVolume.value,
      isPlaying: isPlaying.value,
      queueLength: audioQueue.value.length,
      speechSupported: 'speechSynthesis' in window
    }
  }

  return {
    // 响应式数据
    isAudioEnabled,
    currentVolume,
    isPlaying,

    // 核心播放方法
    playAudio,
    playVoiceMessage,
    playGuidanceAudio,
    playCelebrationAudio,
    playScoreThresholdAudio,
    playFrameWarningAudio,
    playActionCompleteAudio,

    // 控制方法
    stopAllAudio,
    setAudioEnabled,
    setVolume,

    // 工具方法
    testAudio,
    getAudioStatus
  }
}
