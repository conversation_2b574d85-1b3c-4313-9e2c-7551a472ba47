/**
 * 手掌翻转动作检测器
 * 实现旋转角度检测逻辑
 */
import { KeyPointMapping } from '@/utils/poseConstants'
import { useActionDetectionEngine } from '../useActionDetectionEngine'

export function usePalmFlipDetector() {
  const engine = useActionDetectionEngine()

  // 动作所需关键点配置
  const REQUIRED_KEYPOINTS = {
    left: [KeyPointMapping.LEFT_WRIST, KeyPointMapping.LEFT_ELBOW, KeyPointMapping.LEFT_HAND_WRIST],
    right: [KeyPointMapping.RIGHT_WRIST, KeyPointMapping.RIGHT_ELBOW, KeyPointMapping.RIGHT_HAND_WRIST]
  }

  // 备用关键点（如果手部关键点不可用）
  const FALLBACK_KEYPOINTS = {
    left: [KeyPointMapping.LEFT_WRIST, KeyPointMapping.LEFT_ELBOW],
    right: [KeyPointMapping.RIGHT_WRIST, KeyPointMapping.RIGHT_ELBOW]
  }

  // 难度等级配置
  const DIFFICULTY_CONFIG = {
    easy: {
      minFlipAngle: 30,          // 最小翻转角度
      goodFlipAngle: 60,         // 良好翻转角度
      perfectFlipAngle: 90,      // 完美翻转角度
      flipSpeed: 0.5,            // 翻转速度阈值（角度/帧）
      minScore: 50,              // 最低得分
      maxScore: 100              // 最高得分
    },
    medium: {
      minFlipAngle: 45,
      goodFlipAngle: 75,
      perfectFlipAngle: 105,
      flipSpeed: 0.8,
      minScore: 60,
      maxScore: 100
    },
    hard: {
      minFlipAngle: 60,
      goodFlipAngle: 90,
      perfectFlipAngle: 120,
      flipSpeed: 1.0,
      minScore: 70,
      maxScore: 100
    }
  }

  // 翻转历史记录（用于检测翻转动作）
  const flipHistory = new Map()

  /**
   * 检测手掌翻转动作
   * @param {Array} keypoints - 关键点数组
   * @param {string} side - 动作侧面 ('left' | 'right')
   * @param {string} difficultyLevel - 难度等级
   * @param {number} canvasWidth - 画布宽度
   * @param {number} canvasHeight - 画布高度
   * @returns {Object} - 检测结果
   */
  const detectPalmFlip = (keypoints, side = 'left', difficultyLevel = 'easy', canvasWidth = 640, canvasHeight = 480) => {
    const config = DIFFICULTY_CONFIG[difficultyLevel]
    
    // 尝试使用精确的手部关键点
    let detectionResult = detectWithHandKeypoints(keypoints, side, config, canvasWidth, canvasHeight)
    
    // 如果手部关键点不可用，使用手腕和肘部作为备用
    if (detectionResult.stage === 'incomplete') {
      detectionResult = detectWithFallbackKeypoints(keypoints, side, config, canvasWidth, canvasHeight)
    }
    
    return detectionResult
  }

  /**
   * 使用手部关键点进行检测
   * @param {Array} keypoints - 关键点数组
   * @param {string} side - 动作侧面
   * @param {Object} config - 难度配置
   * @param {number} canvasWidth - 画布宽度
   * @param {number} canvasHeight - 画布高度
   * @returns {Object} - 检测结果
   */
  const detectWithHandKeypoints = (keypoints, side, config, canvasWidth, canvasHeight) => {
    const requiredPoints = REQUIRED_KEYPOINTS[side]
    
    // 检查关键点完整性
    const completenessCheck = engine.checkRequiredKeypoints(keypoints, requiredPoints, side, canvasWidth, canvasHeight)
    if (!completenessCheck.isComplete) {
      return {
        accuracy: 0,
        stage: 'incomplete',
        feedback: completenessCheck.message,
        currentAngle: 0,
        flipCount: 0,
        detectionMethod: 'hand_keypoints'
      }
    }

    // 获取关键点
    const wrist = side === 'left' ? 
      keypoints[KeyPointMapping.LEFT_WRIST] : 
      keypoints[KeyPointMapping.RIGHT_WRIST]
    
    const elbow = side === 'left' ? 
      keypoints[KeyPointMapping.LEFT_ELBOW] : 
      keypoints[KeyPointMapping.RIGHT_ELBOW]
    
    const handWrist = side === 'left' ? 
      keypoints[KeyPointMapping.LEFT_HAND_WRIST] : 
      keypoints[KeyPointMapping.RIGHT_HAND_WRIST]

    // 计算前臂角度（手腕到肘部的向量角度）
    const forearmAngle = engine.calculateVectorAngle(elbow, wrist)
    
    // 计算手部角度（手腕到手部中心的向量角度）
    const handAngle = engine.calculateVectorAngle(wrist, handWrist)
    
    // 计算相对角度（手部相对于前臂的角度）
    const relativeAngle = Math.abs(handAngle - forearmAngle)
    
    // 更新翻转历史并检测翻转动作
    const flipData = updateFlipHistory(side, relativeAngle, config)
    
    // 计算得分和阶段
    const result = calculatePalmFlipScore(relativeAngle, flipData, config, side)
    
    return {
      ...result,
      currentAngle: relativeAngle,
      flipCount: flipData.flipCount,
      detectionMethod: 'hand_keypoints'
    }
  }

  /**
   * 使用备用关键点进行检测
   * @param {Array} keypoints - 关键点数组
   * @param {string} side - 动作侧面
   * @param {Object} config - 难度配置
   * @param {number} canvasWidth - 画布宽度
   * @param {number} canvasHeight - 画布高度
   * @returns {Object} - 检测结果
   */
  const detectWithFallbackKeypoints = (keypoints, side, config, canvasWidth, canvasHeight) => {
    const fallbackPoints = FALLBACK_KEYPOINTS[side]
    
    // 检查备用关键点完整性
    const completenessCheck = engine.checkRequiredKeypoints(keypoints, fallbackPoints, side, canvasWidth, canvasHeight)
    if (!completenessCheck.isComplete) {
      return {
        accuracy: 0,
        stage: 'incomplete',
        feedback: '请保持手臂在画面内',
        currentAngle: 0,
        flipCount: 0,
        detectionMethod: 'fallback'
      }
    }

    // 获取关键点
    const wrist = side === 'left' ? 
      keypoints[KeyPointMapping.LEFT_WRIST] : 
      keypoints[KeyPointMapping.RIGHT_WRIST]
    
    const elbow = side === 'left' ? 
      keypoints[KeyPointMapping.LEFT_ELBOW] : 
      keypoints[KeyPointMapping.RIGHT_ELBOW]

    // 计算前臂角度
    const forearmAngle = engine.calculateVectorAngle(elbow, wrist)
    
    // 使用前臂角度变化作为翻转指标
    const flipData = updateFlipHistory(side, forearmAngle, config, true)
    
    // 计算得分和阶段
    const result = calculatePalmFlipScore(forearmAngle, flipData, config, side, true)
    
    return {
      ...result,
      currentAngle: forearmAngle,
      flipCount: flipData.flipCount,
      detectionMethod: 'fallback'
    }
  }

  /**
   * 更新翻转历史记录
   * @param {string} side - 动作侧面
   * @param {number} currentAngle - 当前角度
   * @param {Object} config - 难度配置
   * @param {boolean} isFallback - 是否为备用检测
   * @returns {Object} - 翻转数据
   */
  const updateFlipHistory = (side, currentAngle, config, isFallback = false) => {
    const historyKey = `${side}_${isFallback ? 'fallback' : 'hand'}`
    
    if (!flipHistory.has(historyKey)) {
      flipHistory.set(historyKey, {
        angles: [],
        flipCount: 0,
        lastFlipTime: 0,
        direction: 0 // 1: 正向, -1: 反向, 0: 无方向
      })
    }
    
    const history = flipHistory.get(historyKey)
    const now = Date.now()
    
    // 添加当前角度到历史
    history.angles.push({ angle: currentAngle, timestamp: now })
    
    // 保持历史记录在合理范围内
    if (history.angles.length > 20) {
      history.angles = history.angles.slice(-10)
    }
    
    // 检测翻转动作
    if (history.angles.length >= 3) {
      const recent = history.angles.slice(-3)
      const angleChange = recent[2].angle - recent[0].angle
      
      // 检测方向变化（翻转）
      if (Math.abs(angleChange) > config.minFlipAngle) {
        const newDirection = angleChange > 0 ? 1 : -1
        
        // 如果方向改变且距离上次翻转有足够时间间隔
        if (newDirection !== history.direction && now - history.lastFlipTime > 500) {
          history.flipCount++
          history.lastFlipTime = now
          history.direction = newDirection
        }
      }
    }
    
    return {
      flipCount: history.flipCount,
      direction: history.direction,
      angleChange: history.angles.length >= 2 ? 
        Math.abs(history.angles[history.angles.length - 1].angle - history.angles[history.angles.length - 2].angle) : 0
    }
  }

  /**
   * 计算手掌翻转得分和阶段
   * @param {number} currentAngle - 当前角度
   * @param {Object} flipData - 翻转数据
   * @param {Object} config - 难度配置
   * @param {string} side - 动作侧面
   * @param {boolean} isFallback - 是否为备用检测
   * @returns {Object} - 得分和反馈
   */
  const calculatePalmFlipScore = (currentAngle, flipData, config, side, isFallback = false) => {
    const sideText = side === 'left' ? '左手' : '右手'
    const actionText = isFallback ? '前臂' : '手掌'

    // 基于翻转次数计算基础得分
    let baseScore = Math.min(flipData.flipCount * 25, config.maxScore - 20)
    
    // 基于角度变化计算动态得分
    let dynamicScore = 0
    if (flipData.angleChange > config.flipSpeed) {
      dynamicScore = Math.min(20, flipData.angleChange * 10)
    }
    
    const totalScore = Math.round(baseScore + dynamicScore)
    const accuracy = Math.min(config.maxScore, Math.max(0, totalScore))

    // 判断阶段
    let stage = 'preparing'
    let feedback = `请开始${actionText}翻转动作`

    if (flipData.flipCount >= 4) {
      stage = 'excellent'
      feedback = `${sideText}${actionText}翻转非常好！`
    } else if (flipData.flipCount >= 2) {
      stage = 'good_flipping'
      feedback = `${sideText}${actionText}翻转很好，继续！`
    } else if (flipData.flipCount >= 1) {
      stage = 'flipping'
      feedback = `${sideText}${actionText}开始翻转，继续动作！`
    } else if (flipData.angleChange > config.flipSpeed) {
      stage = 'moving'
      feedback = `${sideText}${actionText}正在移动，加大翻转幅度`
    }

    return {
      accuracy,
      stage,
      feedback
    }
  }

  /**
   * 检查动作是否完成
   * @param {Object} detectionResult - 检测结果
   * @param {string} difficultyLevel - 难度等级
   * @returns {boolean} - 是否完成
   */
  const isActionCompleted = (detectionResult, difficultyLevel = 'easy') => {
    const config = DIFFICULTY_CONFIG[difficultyLevel]
    return detectionResult.accuracy >= config.minScore + 30 && 
           detectionResult.flipCount >= 3
  }

  /**
   * 获取动作指导建议
   * @param {Object} detectionResult - 检测结果
   * @param {string} side - 动作侧面
   * @returns {string} - 指导建议
   */
  const getGuidance = (detectionResult, side) => {
    const sideText = side === 'left' ? '左手' : '右手'
    const isFallback = detectionResult.detectionMethod === 'fallback'
    const actionText = isFallback ? '前臂' : '手掌'

    switch (detectionResult.stage) {
      case 'incomplete':
        return detectionResult.feedback
      case 'preparing':
        return `将${sideText}放在平面上，开始${actionText}翻转`
      case 'moving':
        return `继续${actionText}翻转，加大动作幅度`
      case 'flipping':
        return `很好！继续${actionText}翻转动作`
      case 'good_flipping':
        return `${actionText}翻转节奏很好，保持！`
      case 'excellent':
        return `完美的${actionText}翻转！`
      default:
        return '请按照示范动作进行'
    }
  }

  /**
   * 重置翻转历史
   * @param {string} side - 动作侧面（可选，不提供则重置所有）
   */
  const resetFlipHistory = (side = null) => {
    if (side) {
      flipHistory.delete(`${side}_hand`)
      flipHistory.delete(`${side}_fallback`)
    } else {
      flipHistory.clear()
    }
  }

  return {
    // 主要检测方法
    detectPalmFlip,
    isActionCompleted,
    getGuidance,
    resetFlipHistory,
    
    // 配置
    REQUIRED_KEYPOINTS,
    FALLBACK_KEYPOINTS,
    DIFFICULTY_CONFIG
  }
}
