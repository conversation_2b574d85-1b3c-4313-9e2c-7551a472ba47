<!-- components/VideoStream.vue -->
<template>
  <div ref="containerRef" class="video-stream-wrapper w-full h-full">
    <!-- 加载状态 -->
    <div v-if="!hasVideoData" class="loading-state">
      <div class="flex flex-col items-center">
        <el-icon class="animate-spin text-2xl text-blue-500 mb-2"><Loading /></el-icon>
        <span class="text-gray-600 text-sm">等待视频数据...</span>
      </div>
    </div>

    <!-- 视频流 -->
    <img
      ref="imageRef"
      v-show="hasVideoData && !hasError"
      :src="currentFrameUrl"
      :alt="alt"
      @load="handleImageLoad"
      @error="handleImageError"
      class="camera-image w-full h-full object-cover"
    />

    <!-- 错误状态 -->
    <div v-if="hasError" class="error-state">
      <span class="text-red-600 text-center px-4">
        视频流连接失败
        <br>
        <span class="text-sm text-red-400">正在尝试恢复...</span>
      </span>
    </div>

    <!-- 调试信息 -->
    <div v-if="showDebugInfo" class="debug-info">
      <div>FPS: {{ performanceStats.fps }}</div>
      <div>帧数: {{ frameCount }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onUnmounted, watch } from 'vue';
import { Loading } from '@element-plus/icons-vue';
import { storeToRefs } from 'pinia';
import { useConnectionStore } from '@/stores/connection';

const props = defineProps({
  alt: { type: String, default: '实时视频流' },
  showDebugInfo: { type: Boolean, default: false },
});

const emit = defineEmits(['load', 'error']);

const connectionStore = useConnectionStore();
const { frameData, frameCount } = storeToRefs(connectionStore);

const containerRef = ref(null);
const imageRef = ref(null); // 暴露内部的img元素
const hasError = ref(false);
const currentFrameUrl = ref('');

// 性能统计
let frameRenderCount = 0;
let fpsCalculationTime = Date.now();
const performanceStats = ref({ fps: 0 });

const hasVideoData = computed(() => frameData.value && frameData.value.length > 0);

const handleImageLoad = () => {
  hasError.value = false;
  emit('load');
};

const handleImageError = () => {
  hasError.value = true;
  emit('error', { message: '视频帧加载失败' });
};

watch(frameData, (newFrameData) => {
  if (newFrameData) {
    currentFrameUrl.value = newFrameData;
    frameRenderCount++;
    const now = Date.now();
    if (now - fpsCalculationTime >= 1000) {
      performanceStats.value.fps = frameRenderCount;
      frameRenderCount = 0;
      fpsCalculationTime = now;
    }
  }
}, { immediate: true });

onUnmounted(() => {
 
});

// 暴露容器和图像的引用
defineExpose({
  containerRef,
  imageRef
});
</script>

<style scoped>
.video-stream-wrapper {
  position: relative;
  overflow: hidden;
  background-color: #f5f5f5;
}
.camera-image {
  display: block;
  object-fit: cover; /* 保持 cover 以填满容器 */
}
.loading-state, .error-state {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}
.error-state { background-color: #fee2e2; }
.debug-info { position: absolute; top: 0.5rem; left: 0.5rem; background-color: rgba(0,0,0,0.5); color: white; font-size: 0.75rem; padding: 0.25rem 0.5rem; border-radius: 0.25rem; }
.animate-spin { animation: spin 1s linear infinite; }
@keyframes spin { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }
</style>