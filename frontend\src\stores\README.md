# 模块化Store架构

本项目已重构为模块化的Pinia store架构，将原来臃肿的单一store拆分为多个专门的store模块。

## Store模块

### 1. ConnectionStore (`connection.js`)
**职责**: WebSocket连接状态和实时数据管理
- 连接状态管理、实时帧数据接收、姿态关键点数据、患者ID检测数据

### 2. PatientStore (`patient.js`)
**职责**: 患者信息和校验管理
- 患者身份识别、用户信息管理、患者校验逻辑

### 3. TrainingStore (`training.js`)
**职责**: 训练动作和进度管理
- 康复动作列表、当前动作状态、训练会话管理、训练报告生成

### 4. WorkflowStore (`workflow.js`)
**职责**: 工作流状态转换管理
- 应用状态机、状态转换逻辑、暂停/恢复控制

### 5. NotificationStore (`notification.js`)
**职责**: 消息通知管理
- 系统消息显示、通知历史记录、用户反馈提示

## 使用方式
```javascript
import { useConnectionStore } from '@/stores/connection'
import { usePatientStore } from '@/stores/patient'
// 或使用composables
import { useStateTransition } from '@/composables/useStateTransition'
```
