/**
 * 动作检测逻辑
 * 基于姿态关键点实现动作识别和评分
 */
import { ref, computed } from 'vue'

export function useActionDetection() {
  // 动作检测状态
  const isDetecting = ref(false)
  const detectionHistory = ref([])
  const currentScore = ref(0)
  const actionProgress = ref(0)

  // 动作完成阈值配置
  const COMPLETION_THRESHOLDS = {
    shoulder_touch: 85,
    arm_raise: 80,
    finger_touch: 90,
    palm_flip: 75
  }

  // 动作保持时间要求（毫秒）
  const HOLD_DURATION = {
    shoulder_touch: 1000,
    arm_raise: 1500,
    finger_touch: 800,
    palm_flip: 1200
  }

  /**
   * 计算两点之间的距离
   */
  const calculateDistance = (point1, point2) => {
    if (!point1 || !point2 || !point1.x || !point1.y || !point2.x || !point2.y) {
      return Infinity
    }
    const dx = point1.x - point2.x
    const dy = point1.y - point2.y
    return Math.sqrt(dx * dx + dy * dy)
  }

  /**
   * 计算角度
   */
  const calculateAngle = (point1, point2, point3) => {
    if (!point1 || !point2 || !point3) return 0
    
    const vector1 = { x: point1.x - point2.x, y: point1.y - point2.y }
    const vector2 = { x: point3.x - point2.x, y: point3.y - point2.y }
    
    const dot = vector1.x * vector2.x + vector1.y * vector2.y
    const mag1 = Math.sqrt(vector1.x * vector1.x + vector1.y * vector1.y)
    const mag2 = Math.sqrt(vector2.x * vector2.x + vector2.y * vector2.y)
    
    if (mag1 === 0 || mag2 === 0) return 0
    
    const cosAngle = dot / (mag1 * mag2)
    return Math.acos(Math.max(-1, Math.min(1, cosAngle))) * (180 / Math.PI)
  }

  /**
   * 检测对侧触肩动作
   */
  const detectShoulderTouch = (keypoints) => {
    if (!keypoints || keypoints.length < 17) return 0

    try {
      // 关键点索引（基于COCO格式）
      const leftWrist = keypoints[9]
      const rightWrist = keypoints[10]
      const leftShoulder = keypoints[5]
      const rightShoulder = keypoints[6]

      if (!leftWrist || !rightWrist || !leftShoulder || !rightShoulder) return 0

      // 检测左手触右肩
      const leftHandToRightShoulder = calculateDistance(leftWrist, rightShoulder)
      // 检测右手触左肩
      const rightHandToLeftShoulder = calculateDistance(rightWrist, leftShoulder)

      // 肩膀宽度作为参考
      const shoulderWidth = calculateDistance(leftShoulder, rightShoulder)
      if (shoulderWidth === 0) return 0

      // 计算触肩得分（距离越近得分越高）
      const leftTouchScore = Math.max(0, 100 - (leftHandToRightShoulder / shoulderWidth) * 200)
      const rightTouchScore = Math.max(0, 100 - (rightHandToLeftShoulder / shoulderWidth) * 200)

      // 取较高的得分
      return Math.round(Math.max(leftTouchScore, rightTouchScore))
    } catch (error) {
      console.warn('[ActionDetection] 肩部触摸检测错误:', error)
      return 0
    }
  }

  /**
   * 检测手臂上举动作
   */
  const detectArmRaise = (keypoints) => {
    if (!keypoints || keypoints.length < 17) return 0

    try {
      const leftWrist = keypoints[9]
      const rightWrist = keypoints[10]
      const leftElbow = keypoints[7]
      const rightElbow = keypoints[8]
      const leftShoulder = keypoints[5]
      const rightShoulder = keypoints[6]
      const nose = keypoints[0]

      if (!leftWrist || !rightWrist || !leftShoulder || !rightShoulder || !nose) return 0

      // 计算手腕相对于头部的高度
      const leftWristHeight = nose.y - leftWrist.y
      const rightWristHeight = nose.y - rightWrist.y

      // 计算肩膀到头部的距离作为参考
      const shoulderToHead = Math.abs(nose.y - leftShoulder.y)
      if (shoulderToHead === 0) return 0

      // 计算举起高度得分
      const leftHeightScore = Math.max(0, Math.min(100, (leftWristHeight / shoulderToHead) * 80))
      const rightHeightScore = Math.max(0, Math.min(100, (rightWristHeight / shoulderToHead) * 80))

      // 检查手臂伸直程度
      let straightnessBonus = 0
      if (leftElbow && rightElbow) {
        const leftArmAngle = calculateAngle(leftWrist, leftElbow, leftShoulder)
        const rightArmAngle = calculateAngle(rightWrist, rightElbow, rightShoulder)
        
        // 手臂越直得分越高
        const leftStraightness = Math.max(0, (leftArmAngle - 120) / 60 * 20)
        const rightStraightness = Math.max(0, (rightArmAngle - 120) / 60 * 20)
        straightnessBonus = Math.max(leftStraightness, rightStraightness)
      }

      return Math.round(Math.max(leftHeightScore, rightHeightScore) + straightnessBonus)
    } catch (error) {
      console.warn('[ActionDetection] 手臂上举检测错误:', error)
      return 0
    }
  }

  /**
   * 检测指尖对触动作
   */
  const detectFingerTouch = (keypoints) => {
    if (!keypoints || keypoints.length < 21) return 0

    try {
      // 这里需要手部关键点数据，如果没有详细的手部数据，使用手腕位置估算
      const leftWrist = keypoints[9]
      const rightWrist = keypoints[10]

      if (!leftWrist || !rightWrist) return 0

      // 计算双手距离
      const handsDistance = calculateDistance(leftWrist, rightWrist)
      
      // 估算手掌大小（基于肩膀宽度）
      const leftShoulder = keypoints[5]
      const rightShoulder = keypoints[6]
      if (!leftShoulder || !rightShoulder) return 0
      
      const shoulderWidth = calculateDistance(leftShoulder, rightShoulder)
      const estimatedHandSize = shoulderWidth * 0.15 // 手掌大约是肩宽的15%

      // 距离越近得分越高
      const touchScore = Math.max(0, 100 - (handsDistance / estimatedHandSize) * 100)
      
      return Math.round(touchScore)
    } catch (error) {
      console.warn('[ActionDetection] 指尖对触检测错误:', error)
      return 0
    }
  }

  /**
   * 检测手掌翻转动作
   */
  const detectPalmFlip = (keypoints) => {
    if (!keypoints || keypoints.length < 17) return 0

    try {
      const leftWrist = keypoints[9]
      const rightWrist = keypoints[10]
      const leftElbow = keypoints[7]
      const rightElbow = keypoints[8]

      if (!leftWrist || !rightWrist || !leftElbow || !rightElbow) return 0

      // 计算前臂角度变化（简化版本）
      const leftForearmAngle = Math.atan2(leftWrist.y - leftElbow.y, leftWrist.x - leftElbow.x)
      const rightForearmAngle = Math.atan2(rightWrist.y - rightElbow.y, rightWrist.x - rightElbow.x)

      // 这里需要记录角度变化历史来检测翻转
      // 简化版本：检查手腕相对于肘部的位置变化
      const leftFlipScore = Math.abs(Math.sin(leftForearmAngle)) * 100
      const rightFlipScore = Math.abs(Math.sin(rightForearmAngle)) * 100

      return Math.round(Math.max(leftFlipScore, rightFlipScore))
    } catch (error) {
      console.warn('[ActionDetection] 手掌翻转检测错误:', error)
      return 0
    }
  }

  /**
   * 根据动作类型检测动作完成度
   */
  const detectAction = (actionType, keypoints) => {
    if (!actionType || !keypoints) return 0

    let score = 0
    
    switch (actionType) {
      case 'shoulder_touch':
        score = detectShoulderTouch(keypoints)
        break
      case 'arm_raise':
        score = detectArmRaise(keypoints)
        break
      case 'finger_touch':
        score = detectFingerTouch(keypoints)
        break
      case 'palm_flip':
        score = detectPalmFlip(keypoints)
        break
      default:
        console.warn(`[ActionDetection] 未知动作类型: ${actionType}`)
        return 0
    }

    currentScore.value = score
    
    // 记录检测历史
    detectionHistory.value.push({
      timestamp: Date.now(),
      actionType,
      score,
      keypoints: keypoints.length
    })

    // 保持历史记录在合理范围内
    if (detectionHistory.value.length > 100) {
      detectionHistory.value = detectionHistory.value.slice(-50)
    }

    return score
  }

  /**
   * 检查动作是否完成
   */
  const isActionCompleted = (actionType, currentScore, holdDuration = 0) => {
    const threshold = COMPLETION_THRESHOLDS[actionType] || 80
    const requiredHoldTime = HOLD_DURATION[actionType] || 1000

    if (currentScore >= threshold) {
      if (holdDuration >= requiredHoldTime) {
        return true
      }
    }
    
    return false
  }

  /**
   * 获取动作反馈
   */
  const getActionFeedback = (actionType, score) => {
    if (score >= 95) {
      return { level: 'excellent', message: '完美！动作非常标准' }
    } else if (score >= 80) {
      return { level: 'good', message: '很好！继续保持' }
    } else if (score >= 60) {
      return { level: 'fair', message: '不错，可以更标准一些' }
    } else {
      return { level: 'poor', message: '请参考标准动作调整姿态' }
    }
  }

  /**
   * 重置检测状态
   */
  const resetDetection = () => {
    isDetecting.value = false
    detectionHistory.value = []
    currentScore.value = 0
    actionProgress.value = 0
  }

  /**
   * 开始检测
   */
  const startDetection = () => {
    isDetecting.value = true
    console.log('[ActionDetection] 开始动作检测')
  }

  /**
   * 停止检测
   */
  const stopDetection = () => {
    isDetecting.value = false
    console.log('[ActionDetection] 停止动作检测')
  }

  return {
    // 响应式数据
    isDetecting,
    detectionHistory,
    currentScore,
    actionProgress,

    // 方法
    detectAction,
    isActionCompleted,
    getActionFeedback,
    startDetection,
    stopDetection,
    resetDetection,

    // 工具方法
    calculateDistance,
    calculateAngle
  }
}
