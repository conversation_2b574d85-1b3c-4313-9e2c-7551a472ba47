/**
 * Pinia Store 统一导出和初始化 (模块化版本)
 * 提供所有store的统一访问入口和初始化管理
 */
import { useConnectionStore } from './connection'
import { usePatientStore } from './patient'
import { useTrainingStore } from './training'
import { useWorkflowStore } from './workflow'
import { useNotificationStore } from './notification'

/**
 * 导出所有store
 */
export {
  // 新的模块化store
  useConnectionStore,
  usePatientStore,
  useTrainingStore,
  useWorkflowStore,
  useNotificationStore
}

/**
 * 初始化所有store (模块化版本)
 * 在应用启动时调用，主要初始化store实例
 */
export async function initializeStores() {
  console.log('开始初始化所有Pinia Store...')

  try {
    // 获取所有store实例
    const connectionStore = useConnectionStore()
    const patientStore = usePatientStore()
    const trainingStore = useTrainingStore()
    const workflowStore = useWorkflowStore()
    const notificationStore = useNotificationStore()

    console.log('所有Pinia Store初始化完成')

    return {
      connectionStore,
      patientStore,
      trainingStore,
      workflowStore,
      notificationStore,
    }

  } catch (error) {
    console.error('Store初始化失败:', error)
    throw error
  }
}


/**
 * 重置所有store状态
 * 用于系统重启或用户切换时清理状态
 */
export function resetAllStores() {
  console.log('重置所有Store状态...')

  // 重置所有模块化store
  const connectionStore = useConnectionStore()
  const patientStore = usePatientStore()
  const trainingStore = useTrainingStore()
  const workflowStore = useWorkflowStore()
  const notificationStore = useNotificationStore()

  connectionStore.resetData()
  patientStore.resetPatientData()
  trainingStore.resetTrainingData()
  workflowStore.resetWorkflow()
  notificationStore.resetNotificationData()

  console.log('所有Store状态已重置')
}

/**
 * 获取应用整体状态摘要
 * 用于调试和状态监控
 */
export function getAppStateSummary() {
  const connectionStore = useConnectionStore()
  const patientStore = usePatientStore()
  const workflowStore = useWorkflowStore()

  return {
    timestamp: new Date().toISOString(),
    currentState: workflowStore.currentState,
    isConnected: connectionStore.isConnected,
    isUserLoggedIn: patientStore.isUserLoggedIn,
  }
}

/**
 * 开发环境调试工具
 */
if (import.meta.env.DEV) {
  // 暴露到全局以便调试
  window.getAppStateSummary = getAppStateSummary
  window.resetAllStores = resetAllStores
  // 定期输出状态摘要（开发模式）
  setInterval(() => {
    console.log('应用状态摘要:', getAppStateSummary())
  }, 30000) // 每30秒输出一次
}
