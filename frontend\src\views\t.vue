好的，你当前的登录页已经有了一个非常棒的现代美学基础——渐变背景、玻璃拟物（Glassmorphism）效果、清晰的布局，非常不错！

我们的目标是在这个基础上进行“美化”和“优化”，主要集中在以下几个方面：

1.  **提升视觉焦点**：将用户的注意力更强烈地引导到核心交互区域（视频流）。
2.  **增强氛围感**：通过更精致的动画和布局，让“智能”、“科技”的感觉更上一层楼。
3.  **优化信息层级**：减少信息冗余，让界面看起来更简洁、更优雅。
4.  **打造惊艳的过渡动画**：将“登录成功”从一个简单的信息提示，变成一个令人愉悦、具有仪式感的视觉体验。

---

### 第一步：UI界面美化重构

我们将从双栏布局调整为更加聚焦的单体核心布局，将所有信息和交互都围绕着最重要的“实时画面”展开。

**这是重构后的完整代码：**

```vue
<template>
  <!-- 1. 背景层：增加动态光晕效果，提升氛围 -->
  <div class="login-page">
    <div class="circle-bg circle-1"></div>
    <div class="circle-bg circle-2"></div>
    <div class="circle-bg circle-3"></div>

    <!-- 2. 内容层：使用 transition-group 实现不同状态间的平滑切换 -->
    <transition name="fade-scale" mode="out-in">
      <!-- 登录/识别状态 -->
      <div v-if="!showSuccessPortal" key="login" class="login-container">
        <!-- 系统标题 -->
        <header class="system-header">
          <h1 class="system-title">
            智能康复系统
          </h1>
          <p class="system-subtitle">
            Intelligent Rehabilitation System
          </p>
        </header>

        <!-- 核心交互卡片 -->
        <main class="video-card">
          <!-- 视频流容器，现在是绝对的视觉中心 -->
          <div class="video-stream-wrapper" :class="{ 'is-detecting': currentState === 'waiting' }">
            <!-- 新增：科技感扫描线动画 -->
            <div class="scanner-line"></div>
            
            <VideoStream :width="'100%'" :height="'100%'" class="video-stream" />
            
            <!-- 人脸检测框 (样式微调) -->
            <div v-if="currentState === 'waiting' && faceBbox" class="face-bbox-container">
              <div :style="faceBboxStyle" class="face-bbox">
                <div v-if="detectedPatientId" class="detected-id-tag">
                  ID: {{ detectedPatientId }}
                </div>
              </div>
            </div>
          </div>

          <!-- 状态信息区域 (整合到卡片下方) -->
          <footer class="status-footer">
            <transition name="fade-scale" mode="out-in">
              <!-- 等待状态 -->
              <div v-if="currentState === 'waiting'" key="waiting" class="status-content">
                <el-icon class="status-icon is-loading"><Loading /></el-icon>
                <h2 class="status-title">等待身份识别</h2>
                <p class="status-description">请正对摄像头，系统将自动验证您的身份</p>
              </div>
              <!-- 失败状态 -->
              <div v-else-if="currentState === 'start_failed'" key="failed" class="status-content">
                <el-icon class="status-icon is-error"><WarningFilled /></el-icon>
                <h2 class="status-title text-red-400">连接失败</h2>
                <p class="status-description">{{ connectionError || '无法连接到核心服务' }}</p>
              </div>
              <!-- 其他状态 -->
              <div v-else key="other" class="status-content">
                 <el-icon class="status-icon is-success"><CircleCheckFilled /></el-icon>
                 <h2 class="status-title text-emerald-400">{{ systemStatusText }}</h2>
                 <p class="status-description">{{ message || '系统运行正常' }}</p>
              </div>
            </transition>
          </footer>
        </main>

        <!-- 底部信息 -->
        <footer class="page-footer">
          系统版本 v1.0.0 | 连接状态: 
          <span :class="isConnected ? 'text-green-400' : 'text-red-400'">
            {{ isConnected ? '已连接' : '未连接' }}
          </span>
        </footer>
      </div>

      <!-- 登录成功过渡动画 -->
      <div v-else key="success" class="success-portal-container">
        <div class="success-content">
          <el-icon class="success-icon"><CircleCheckFilled /></el-icon>
          <h2 class="success-title">验证成功</h2>
          <p class="success-welcome">欢迎您，{{ userDisplayName }}</p>
          <p class="success-redirect">正在进入训练系统...</p>
        </div>
      </div>
    </transition>
  </div>
</template>

<style scoped>
/* --- 全局与背景 --- */
.login-page {
  @apply h-screen overflow-hidden relative bg-gray-900 flex items-center justify-center p-4;
  background: radial-gradient(ellipse at bottom, #1b2735 0%, #090a0f 100%);
}

.circle-bg {
  @apply absolute rounded-full filter blur-3xl opacity-30 pointer-events-none;
  animation: float 20s infinite ease-in-out;
}
.circle-1 {
  @apply w-96 h-96 bg-indigo-600 top-[-10%] left-[-5%];
  animation-delay: 0s;
}
.circle-2 {
  @apply w-80 h-80 bg-purple-600 bottom-[-10%] right-[-5%];
  animation-delay: 5s;
}
.circle-3 {
  @apply w-72 h-72 bg-pink-600 bottom-[20%] left-[15%];
  animation-delay: 10s;
}

@keyframes float {
  0%, 100% { transform: translateY(0) translateX(0) scale(1); }
  50% { transform: translateY(-20px) translateX(20px) scale(1.1); }
}

/* --- 容器与布局 --- */
.login-container {
  @apply w-full max-w-2xl flex flex-col items-center gap-8 text-white;
}

.system-header {
  @apply text-center;
}

.system-title {
  @apply text-4xl lg:text-5xl font-bold tracking-wider;
  background: linear-gradient(120deg, #e0e0e0, #ffffff, #e0e0e0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 100%;
  animation: shine-text 5s linear infinite;
}

@keyframes shine-text {
  to { background-position: -200% 0; }
}

.system-subtitle {
  @apply text-lg text-white/60 font-light tracking-widest mt-2 uppercase;
}

/* --- 核心视频卡片 --- */
.video-card {
  @apply w-full max-w-lg aspect-square flex flex-col rounded-3xl p-6 border;
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.video-stream-wrapper {
  @apply flex-1 relative rounded-2xl overflow-hidden border-2 border-transparent transition-all duration-500;
}

/* 关键：识别中的高亮状态 */
.video-stream-wrapper.is-detecting {
  border-color: #6366f1; /* Indigo-500 */
  box-shadow: 0 0 20px rgba(99, 102, 241, 0.5);
}

.video-stream {
  @apply w-full h-full object-cover;
}

/* 新增：扫描线动画 */
.scanner-line {
  @apply absolute top-0 left-0 w-full h-1 bg-cyan-400/80 rounded-full shadow-lg z-10;
  box-shadow: 0 0 10px #22d3ee, 0 0 20px #22d3ee;
  animation: scan-anim 3s infinite cubic-bezier(0.65, 0, 0.35, 1);
  opacity: 0;
}
.video-stream-wrapper.is-detecting .scanner-line {
  opacity: 1;
}

@keyframes scan-anim {
  0% { transform: translateY(-100%); opacity: 0; }
  10%, 90% { opacity: 1; }
  100% { transform: translateY(calc(var(--video-height, 400px) + 100%)); opacity: 0; }
}
/* 需要在onMounted中设置CSS变量--video-height */

/* --- 人脸框样式 --- */
.face-bbox-container {
  @apply absolute inset-0 pointer-events-none;
}
.face-bbox {
  @apply absolute border-2 rounded-lg transition-all duration-200;
  border-color: #34d399; /* Emerald-400 */
  background-color: rgba(52, 211, 153, 0.1);
  box-shadow: 0 0 15px rgba(52, 211, 153, 0.4);
}
.detected-id-tag {
  @apply absolute -top-8 left-0 bg-emerald-500 text-white text-xs font-bold px-3 py-1 rounded;
}


/* --- 状态信息区域 --- */
.status-footer {
  @apply h-32 pt-6 text-center;
}
.status-content {
  @apply flex flex-col items-center justify-center gap-2;
}
.status-icon {
  @apply text-3xl mb-2;
}
.status-icon.is-loading { @apply text-indigo-400 animate-spin; }
.status-icon.is-error { @apply text-red-400; }
.status-icon.is-success { @apply text-emerald-400; }

.status-title {
  @apply text-xl font-semibold text-white/90;
}
.status-description {
  @apply text-sm text-white/60 max-w-xs;
}

.page-footer {
  @apply text-center text-xs text-white/40 tracking-wider;
}

/* --- 页面切换动画 --- */
.fade-scale-enter-active,
.fade-scale-leave-active {
  transition: opacity 0.4s ease, transform 0.4s ease;
}
.fade-scale-enter-from,
.fade-scale-leave-to {
  opacity: 0;
  transform: scale(0.95);
}


/* --- 登录成功过渡动画 --- */
.success-portal-container {
  @apply fixed inset-0 z-50 flex items-center justify-center;
  /* 关键：使用 clip-path 实现 "传送门" 打开效果 */
  animation: portal-open 0.8s cubic-bezier(0.76, 0, 0.24, 1) forwards;
}

.success-content {
  @apply text-center text-white flex flex-col items-center gap-4;
  /* 内容淡入效果，比背景慢一点出现 */
  animation: content-fade-in 1.2s ease forwards;
  opacity: 0;
}
@keyframes content-fade-in {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.success-icon {
  @apply text-7xl text-white;
  /* 图标的"打勾"动画 */
  transform: scale(0);
  animation: check-pop-in 0.5s 0.6s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}
@keyframes check-pop-in {
  to { transform: scale(1); }
}

.success-title {
  @apply text-4xl font-bold;
}
.success-welcome {
  @apply text-2xl text-white/80;
}
.success-redirect {
  @apply text-base text-white/60;
}

@keyframes portal-open {
  from {
    clip-path: circle(0% at 50% 50%);
  }
  to {
    clip-path: circle(75% at 50% 50%);
  }
}
</style>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useConnectionStore } from '@/stores/connection'
import { usePatientStore } from '@/stores/patient'
import { useNotificationStore } from '@/stores/notification'
import { useWorkflowStore } from '@/stores/workflow'
import { useStateTransition } from '@/composables/useStateTransition'
import { CircleCheckFilled, Loading, WarningFilled, Monitor } from '@element-plus/icons-vue'
import VideoStream from '@/components/VideoStream.vue'
import { useConnectionStore } from '@/stores/connection'
const patientStore = usePatientStore()
const notificationStore = useNotificationStore()
const workflowStore = useWorkflowStore()
const stateTransition = useStateTransition()
const connectionStore = useConnectionStore()
// 新增 ref 用于控制 "传送门" 动画
const showSuccessPortal = ref(false);
// 人脸检测相关
const faceBbox = ref(null)
const detectedPatientId = ref(null)
// ... 其他计算属性 ...
// 计算属性
const isConnected = computed(() => connectionStore.isConnected)
const currentState = computed(() => workflowStore.currentState)
const userInfo = computed(() => patientStore.userInfo)
const message = computed(() => notificationStore.message)
const connectionError = computed(() => connectionStore.connectionError)

// 响应式消息系统
const currentNotification = computed(() => notificationStore.currentNotification)
const faceBboxStyle = computed(() => {
  if (!faceBbox.value) return {};
  return {
    left: faceBbox.value.x + 'px',
    top: faceBbox.value.y + 'px',
    width: faceBbox.value.width + 'px',
    height: faceBbox.value.height + 'px',
  };
});

// 重写原有的 showLoginSuccessAnimation 函数
const showLoginSuccessAnimation = () => {
  console.log('显示登录成功动画 - 新版传送门');
  // 1. 触发传送门动画
  showSuccessPortal.value = true;
  // 2. 动画结束后（约1.5秒后）执行跳转逻辑
  setTimeout(() => {
    console.log('传送门动画完成，触发状态转换到任务介绍页面');
    stateTransition.handlePatientValidationSuccess();
    // （可选）为下一个页面的进入做准备，重置状态
    // setTimeout(() => showSuccessPortal.value = false, 500);
  }, 1500); // 这个时间应与CSS动画总时长匹配
};

// 监听器保持不变
watch(
  () => patientStore.userInfo,
  (newUserInfo) => {
    if (newUserInfo && newUserInfo.patient_id && !showSuccessPortal.value) { // 增加一个判断，防止重复触发
      console.log('检测到用户信息更新，触发登录成功动画');
      showLoginSuccessAnimation();
    }
  },
  { immediate: false }
);

// 新增 onMounted 逻辑来设置CSS变量
onMounted(() => {
  // 为扫描线动画设置容器高度
  const videoWrapper = document.querySelector('.video-stream-wrapper');
  if (videoWrapper) {
    const height = videoWrapper.offsetHeight;
    document.documentElement.style.setProperty('--video-height', `${height}px`);
  }
  // 如果当前状态是waiting且已连接，启动patientId校验
  if (workflowStore.currentState === 'waiting' && connectionStore.isConnected) {
    console.log('开始启动patientId校验');
    patientStore.startPatientValidation(() => connectionStore.patientId);
  }
});

onUnmounted(() => {
  console.log('LoginView 组件即将卸载，停止patientId校验');
  patientStore.stopPatientValidation();
});
</script>
